# 🏢 CA Firm Document Management System

AI-powered document management and search system for CA firms with Dropbox integration.

## 🚀 Features

- **🔗 Dropbox Integration**: Automatic document synchronization
- **🔍 AI Search**: Semantic search using vector embeddings  
- **🔑 API Management**: Client API access with rate limiting
- **📊 Analytics**: Usage tracking and performance monitoring
- **🔒 Security**: JWT authentication and secure access control

## 📁 Project Structure

```
app/
├── main.py                    # FastAPI entrypoint
├── core/                      # Configuration, database, security
├── models/                    # SQLAlchemy database models
├── schemas/                   # Pydantic request/response schemas
├── api/
│   ├── owner/                 # Owner-specific routes (dashboard, Dropbox, etc.)
│   └── client/                # Client-specific routes (search, documents)
├── services/                  # Business logic (embeddings, search, etc.)
├── workers/                   # Celery background tasks
└── utils/                     # Helper functions
```

## ⚡ Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**
   ```bash
   cp env.example .env
   # Edit .env with your database and other settings
   ```

3. **Run the Application**
   ```bash
   cd app
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

4. **Access the Application**
   - API Documentation: http://localhost:8000/docs
   - Welcome Page: http://localhost:8000/welcome
   - Health Check: http://localhost:8000/health

## 🔧 Development Setup

### Prerequisites
- Python 3.8+
- PostgreSQL 12+
- Redis 6+

### Installation Steps
1. Clone the repository
2. Create virtual environment: `python -m venv venv`
3. Activate virtual environment: `source venv/bin/activate` (Unix) or `venv\Scripts\activate` (Windows)
4. Install dependencies: `pip install -r requirements.txt`
5. Set up environment variables (copy `env.example` to `.env`)
6. Run the application: `uvicorn app.main:app --reload`

## 📊 Development Progress

This project is built incrementally with the following milestones:

- ✅ **Task 1**: Basic project structure and FastAPI setup
- ⏳ **Task 2**: Database configuration and models
- ⏳ **Task 3**: Authentication system
- ⏳ **Task 4**: Owner dashboard
- ⏳ **Task 5+**: Dropbox integration, AI search, client APIs

## 🔑 API Endpoints

### Owner Endpoints
- `POST /api/owner/auth/login` - Owner authentication
- `GET /api/owner/dashboard` - Dashboard overview
- `POST /api/owner/dropbox/connect` - Connect Dropbox
- `GET /api/owner/files` - File management

### Client Endpoints  
- `POST /api/v1/client/search` - Search documents
- `GET /api/v1/client/documents` - List documents
- `GET /api/v1/client/usage` - API usage stats

## 🛠️ Technology Stack

- **Backend**: FastAPI, SQLAlchemy, PostgreSQL
- **Background Tasks**: Celery, Redis
- **AI/ML**: sentence-transformers, vector search
- **Cloud Storage**: Dropbox API
- **Authentication**: JWT tokens
- **Documentation**: Auto-generated OpenAPI/Swagger

## 📞 Support

For questions or issues, please contact the development team.

---

**Built with ❤️ for CA firms to streamline document management and client service.**
