# CA Firm Document Management - Technology Stack

## 🚀 **Application Overview**
A comprehensive document management system for CA (Chartered Accountant) firms with Dropbox integration, AI-powered document search, and automated file processing capabilities.

---

## 🛠️ **Technology Stack**

### **Backend Framework**
- **FastAPI** `0.104.1` - Modern Python web framework for building APIs
- **Uvicorn** `0.24.0` - ASGI web server implementation
- **Python** `3.13` - Programming language

### **Database**
- **PostgreSQL** `15.14` - Primary database (production-ready)
- **SQLAlchemy** `2.0.43` - Python SQL toolkit and ORM
- **Psycopg** `3.2.10` - PostgreSQL adapter for Python
- **Alembic** `1.12.1` - Database migration tool

### **Authentication & Security**
- **Python-JOSE** `3.3.0` - JSON Web Signature implementation
- **Passlib** `1.7.4` - Password hashing library with bcrypt support
- **JWT** - JSON Web Tokens for authentication

### **Background Processing**
- **Celery** `5.3.4` - Distributed task queue
- **Redis** `5.0.1` - Message broker and caching

### **Cloud Storage Integration**
- **Dropbox SDK** `11.36.2` - Official Dropbox API integration
- **OAuth 2.0** - Secure authorization protocol

### **File Processing**
- **PyPDF2** `3.0.1` - PDF file processing
- **python-docx** `1.1.0` - Microsoft Word document processing
- **openpyxl** `3.1.2` - Excel file processing
- **python-magic** `0.4.27` - File type detection

### **AI/ML Capabilities**
- **Sentence Transformers** `2.2.2` - Text embeddings for semantic search
- **NumPy** `1.24.4` - Numerical computing

### **Frontend**
- **HTML5** - Modern web markup
- **Bootstrap 5** - CSS framework for responsive design
- **JavaScript (ES6+)** - Client-side scripting
- **Jinja2** - Python templating engine

### **Development Tools**
- **Pytest** `7.4.3` - Testing framework
- **Black** `23.11.0` - Code formatter
- **Flake8** `6.1.0` - Code linting

### **Configuration Management**
- **Pydantic Settings** `2.0.3` - Settings management with type validation
- **Environment Variables** - Configuration via .env files

---

## 📁 **Application Structure**

```
/Users/<USER>/Documents/n8n/pythonCAFirm/
├── app/                          # Main application directory
│   ├── api/                      # API endpoints
│   │   ├── owner/               # Owner-specific endpoints
│   │   │   ├── auth.py          # Authentication endpoints
│   │   │   ├── dashboard.py     # Dashboard API
│   │   │   ├── documents.py     # Document management
│   │   │   ├── dropbox.py       # Dropbox integration
│   │   │   └── tasks.py         # Task management
│   │   └── client/              # Client-specific endpoints
│   ├── core/                    # Core application modules
│   │   ├── auth.py              # Authentication logic
│   │   ├── config.py            # Configuration settings
│   │   ├── database.py          # Database connection
│   │   └── security.py          # Security utilities
│   ├── models/                  # Database models
│   │   ├── base.py              # Base model class
│   │   ├── owner.py             # Owner/User model
│   │   ├── document.py          # Document model
│   │   ├── dropbox_integration.py # Dropbox integration model
│   │   └── sync_job.py          # Sync job model
│   ├── schemas/                 # Pydantic schemas
│   │   ├── owner.py             # Owner schemas
│   │   ├── document.py          # Document schemas
│   │   ├── dropbox.py           # Dropbox schemas
│   │   └── sync_job.py          # Sync job schemas
│   ├── services/                # Business logic services
│   │   ├── dropbox_service.py   # Dropbox service
│   │   └── file_storage.py      # File storage service
│   ├── templates/               # HTML templates
│   │   ├── base.html            # Base template
│   │   ├── dashboard.html       # Dashboard UI
│   │   ├── login.html           # Login page
│   │   └── register.html        # Registration page
│   ├── static/                  # Static files
│   │   ├── css/                 # Stylesheets
│   │   └── js/                  # JavaScript files
│   ├── workers/                 # Background workers
│   │   ├── celery_app.py        # Celery configuration
│   │   └── tasks/               # Celery tasks
│   │       ├── file_tasks.py    # File processing tasks
│   │       └── test_tasks.py    # Test tasks
│   └── main.py                  # FastAPI application entry point
├── venv/                        # Python virtual environment
├── requirements.txt             # Python dependencies
├── requirements-basic.txt       # Basic dependencies
├── start_worker.py              # Celery worker startup
├── cafirm.db.backup            # SQLite backup (legacy)
└── README.md                    # Project documentation
```

---

## 🗄️ **Database Configuration**

### **Database Details**
- **Database System:** PostgreSQL 15.14
- **Database Name:** `cafirm_db`
- **Host:** `localhost`
- **Port:** `5432`
- **Username:** `indianic`
- **Connection URL:** `postgresql+psycopg://indianic@localhost:5432/cafirm_db`

### **Database Tables**
```sql
-- Core application tables
owner                 -- CA firm owners/users
dropbox_integration   -- Dropbox OAuth credentials and settings
documents            -- Document metadata and processing status
sync_jobs            -- Background synchronization jobs
```

### **Database Management Tools**
- **pgAdmin 4** - Web-based PostgreSQL administration
  - Location: `/Applications/pgAdmin 4.app`
  - Access: `http://127.0.0.1:[dynamic_port]`
- **Command Line:** `/opt/homebrew/opt/postgresql@15/bin/psql cafirm_db`

---

## 🚀 **Application Endpoints**

### **Development Server**
- **URL:** `http://localhost:8000`
- **API Documentation:** `http://localhost:8000/docs`
- **Interactive API:** `http://localhost:8000/redoc`

### **Key API Routes**
```
/api/owner/auth/          # Authentication endpoints
/api/owner/dashboard/     # Dashboard and statistics
/api/owner/dropbox/       # Dropbox integration
/api/owner/documents/     # Document management
/api/owner/tasks/         # Task management
```

---

## 🔧 **Environment Configuration**

### **Required Environment Variables**
```bash
# Database Configuration
POSTGRES_USER=indianic
POSTGRES_PASSWORD=
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=cafirm_db

# Security
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Dropbox Integration
DROPBOX_APP_KEY=your-dropbox-app-key
DROPBOX_APP_SECRET=your-dropbox-app-secret

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_URL=redis://localhost:6379/0
```

---

## 📦 **Installation & Setup**

### **Prerequisites**
- Python 3.13+
- PostgreSQL 15+
- Redis (for background tasks)
- Virtual environment

### **Quick Start**
```bash
# 1. Navigate to project directory
cd /Users/<USER>/Documents/n8n/pythonCAFirm

# 2. Activate virtual environment
source venv/bin/activate

# 3. Install dependencies
pip install -r requirements.txt

# 4. Start PostgreSQL service
brew services start postgresql@15

# 5. Run the application
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 6. Start background worker (separate terminal)
python start_worker.py
```

---

## 🎯 **Key Features**

- **✅ User Authentication** - JWT-based secure authentication
- **✅ Dropbox Integration** - OAuth2 connection with file synchronization
- **✅ Document Management** - Upload, process, and organize documents
- **✅ AI-Powered Search** - Semantic search using sentence transformers
- **✅ Background Processing** - Async file processing with Celery
- **✅ Responsive Dashboard** - Modern Bootstrap-based interface
- **✅ RESTful API** - Complete API documentation with FastAPI
- **✅ Database Migrations** - Alembic-based schema management

---

## 👥 **For Developers**

### **Code Style**
- **Formatter:** Black
- **Linter:** Flake8
- **Type Hints:** Enabled throughout the codebase

### **Testing**
- **Framework:** Pytest
- **Coverage:** Aim for >80% test coverage

### **API Development**
- **Documentation:** Auto-generated with FastAPI
- **Validation:** Pydantic schemas for request/response validation
- **Error Handling:** Structured error responses

---

## 📞 **Support & Maintenance**

- **Application Path:** `/Users/<USER>/Documents/n8n/pythonCAFirm/`
- **Database:** PostgreSQL on localhost:5432
- **Logs:** Check application logs for debugging
- **Backup:** Regular PostgreSQL database backups recommended

---

