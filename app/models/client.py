"""
Client model for CA firm clients
"""
from sqlalchemy import Column, String, Boolean, Text, Integer, ForeignKey, DateTime, Float, JSON
from sqlalchemy.orm import relationship
from .base import BaseModel
from datetime import datetime


class Client(BaseModel):
    """
    Client model - represents clients of the CA firm who can access their documents and use AI bot
    """
    
    # Ownership (which CA firm owner manages this client)
    owner_id = Column(Integer, ForeignKey("owner.id"), nullable=False, index=True)
    
    # Basic client information
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(255), nullable=False)
    company_name = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    
    # Address information
    address_line1 = Column(String(500), nullable=True)
    address_line2 = Column(String(500), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(100), nullable=True)
    
    # Authentication
    hashed_password = Column(String(255), nullable=True)  # For web panel access
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Access settings
    has_web_access = Column(Boolean, default=True, nullable=False)  # Can use web panel
    has_api_access = Column(Boolean, default=False, nullable=False)  # Can use API
    
    # Document access settings (stored as JSON list)
    assigned_folder_paths = Column(JSON, nullable=True)  # List of allowed Dropbox folder paths
    allowed_document_types = Column(JSON, nullable=True)  # List of allowed document types
    allowed_file_extensions = Column(JSON, nullable=True)  # List of allowed file extensions
    
    # Business relationship details
    client_type = Column(String(20), default="individual", nullable=False)  # "individual" or "business"
    industry = Column(String(100), nullable=True)
    annual_revenue = Column(Float, nullable=True)
    tax_id = Column(String(50), nullable=True)
    registration_number = Column(String(100), nullable=True)
    
    # Service details (stored as JSON list)
    services_subscribed = Column(JSON, nullable=True)  # List of subscribed services
    retainer_amount = Column(Float, nullable=True)
    billing_frequency = Column(String(20), default="monthly", nullable=False)  # "monthly", "quarterly", "annual"
    
    # Account settings
    account_start_date = Column(DateTime, default=datetime.utcnow, nullable=False)
    account_end_date = Column(DateTime, nullable=True)
    
    # Notification settings
    email_notifications = Column(Boolean, default=True, nullable=False)
    sms_notifications = Column(Boolean, default=False, nullable=False)
    notification_frequency = Column(String(20), default="weekly", nullable=False)  # "daily", "weekly", "monthly"
    
    # Security settings
    two_factor_enabled = Column(Boolean, default=False, nullable=False)
    password_reset_required = Column(Boolean, default=False, nullable=False)
    session_timeout_minutes = Column(Integer, default=30, nullable=False)
    last_login_at = Column(DateTime, nullable=True)
    
    # AI Bot settings
    bot_access_enabled = Column(Boolean, default=True, nullable=False)
    daily_question_limit = Column(Integer, default=50, nullable=False)
    search_scope = Column(String(50), default="assigned_documents_only", nullable=False)
    include_archived_documents = Column(Boolean, default=False, nullable=False)
    search_history_retention_days = Column(Integer, default=90, nullable=False)
    
    # Usage tracking
    total_questions_asked = Column(Integer, default=0, nullable=False)
    last_question_at = Column(DateTime, nullable=True)
    
    # Relationships
    owner = relationship("Owner", back_populates="clients")
    api_keys = relationship("ClientApiKey", back_populates="client", cascade="all, delete-orphan")
    chat_sessions = relationship("ChatSession", back_populates="client", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Client(email='{self.email}', name='{self.full_name}', type='{self.client_type}')>"
    
    def get_assigned_folder_paths(self):
        """Get assigned folder paths as a list"""
        return self.assigned_folder_paths or []
    
    def get_allowed_document_types(self):
        """Get allowed document types as a list"""
        return self.allowed_document_types or []
    
    def get_allowed_file_extensions(self):
        """Get allowed file extensions as a list"""
        return self.allowed_file_extensions or []
    
    def get_services_subscribed(self):
        """Get subscribed services as a list"""
        return self.services_subscribed or []
    
    def is_within_daily_limit(self):
        """Check if client is within their daily question limit"""
        # This would need to be implemented with actual daily count logic
        return True  # Placeholder for now
    
    def can_access_document(self, document_path: str) -> bool:
        """Check if client can access a specific document based on folder permissions"""
        allowed_paths = self.get_assigned_folder_paths()
        if not allowed_paths:
            return False
        
        return any(document_path.startswith(path) for path in allowed_paths)


class ClientApiKey(BaseModel):
    """
    API keys for client programmatic access
    """
    
    client_id = Column(Integer, ForeignKey("client.id"), nullable=False, index=True)
    api_key = Column(String(255), unique=True, nullable=False, index=True)
    name = Column(String(100), nullable=False)  # Human readable name like "Mobile App", "Integration"
    description = Column(Text, nullable=True)
    
    # Status and permissions
    is_active = Column(Boolean, default=True, nullable=False)
    rate_limit_per_hour = Column(Integer, default=100, nullable=False)
    allowed_endpoints = Column(JSON, nullable=True)  # List of allowed API endpoints
    
    # Timestamps
    expires_at = Column(DateTime, nullable=True)
    last_used_at = Column(DateTime, nullable=True)
    created_by_owner_id = Column(Integer, ForeignKey("owner.id"), nullable=False)  # Which owner created this key
    
    # Usage tracking
    total_requests = Column(Integer, default=0, nullable=False)
    
    # Relationships
    client = relationship("Client", back_populates="api_keys")
    created_by = relationship("Owner")
    
    def __repr__(self):
        return f"<ClientApiKey(name='{self.name}', client='{self.client.full_name}', active={self.is_active})>"
    
    def is_expired(self) -> bool:
        """Check if the API key is expired"""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    def is_valid(self) -> bool:
        """Check if the API key is valid (active and not expired)"""
        return self.is_active and not self.is_expired()


class DocumentEmbedding(BaseModel):
    """
    Store embeddings for semantic search - each document can have multiple chunks
    """
    
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False, index=True)
    chunk_index = Column(Integer, nullable=False)  # For large documents split into chunks
    
    # Text content
    text_chunk = Column(Text, nullable=False)  # The actual text content
    chunk_start_char = Column(Integer, nullable=True)  # Start position in original document
    chunk_end_char = Column(Integer, nullable=True)  # End position in original document
    
    # Embedding data
    embedding_vector = Column(Text, nullable=False)  # JSON serialized vector
    embedding_model = Column(String(100), nullable=False)  # Model used for embedding
    vector_dimension = Column(Integer, nullable=False)  # Dimension of the vector
    
    # Processing metadata
    processing_status = Column(String(20), default="completed", nullable=False)  # "pending", "processing", "completed", "failed"
    processing_error = Column(Text, nullable=True)
    processed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Search optimization
    is_searchable = Column(Boolean, default=True, nullable=False)
    relevance_score = Column(Float, nullable=True)  # For ranking chunks within a document
    
    # Relationships
    document = relationship("Document")
    
    def __repr__(self):
        return f"<DocumentEmbedding(doc_id={self.document_id}, chunk={self.chunk_index}, status='{self.processing_status}')>"
    
    def get_embedding_vector(self):
        """Get embedding vector as a list of floats"""
        import json
        return json.loads(self.embedding_vector)
    
    def set_embedding_vector(self, vector):
        """Set embedding vector from a list of floats"""
        import json
        self.embedding_vector = json.dumps(vector)
        self.vector_dimension = len(vector)


class ChatSession(BaseModel):
    """
    Store client chat sessions with the AI bot
    """
    
    client_id = Column(Integer, ForeignKey("client.id"), nullable=False, index=True)
    session_id = Column(String(255), nullable=False, index=True)  # UUID for grouping related questions
    
    # Question and answer
    question = Column(Text, nullable=False)
    answer = Column(Text, nullable=False)
    
    # Source information
    source_document_ids = Column(JSON, nullable=True)  # List of document IDs used for answer
    source_chunks = Column(JSON, nullable=True)  # List of chunk information
    confidence_score = Column(Float, nullable=True)  # AI confidence in the answer
    
    # Search metadata
    search_query = Column(Text, nullable=True)  # Processed search query
    search_results_count = Column(Integer, nullable=True)  # Number of documents found
    response_time_ms = Column(Integer, nullable=True)  # Response time in milliseconds
    
    # Quality tracking
    user_feedback = Column(String(20), nullable=True)  # "helpful", "not_helpful", "partially_helpful"
    feedback_comment = Column(Text, nullable=True)
    
    # Session info
    ip_address = Column(String(45), nullable=True)  # Client IP address
    user_agent = Column(Text, nullable=True)  # Browser/app information
    
    # Relationships
    client = relationship("Client", back_populates="chat_sessions")
    
    def __repr__(self):
        return f"<ChatSession(client='{self.client.full_name}', session='{self.session_id[:8]}...', created='{self.created_at}')>"
    
    def get_source_document_ids(self):
        """Get source document IDs as a list"""
        return self.source_document_ids or []
    
    def get_source_chunks(self):
        """Get source chunks information as a list"""
        return self.source_chunks or []
    
    def set_source_documents(self, document_ids, chunks_info=None):
        """Set source documents and chunks information"""
        self.source_document_ids = document_ids
        if chunks_info:
            self.source_chunks = chunks_info
