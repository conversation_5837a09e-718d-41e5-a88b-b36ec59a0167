"""
Dropbox integration model for storing OAuth tokens and settings
"""
from sqlalchemy import Column, String, Boolean, Text, Integer, ForeignKey
from sqlalchemy.orm import relationship
from .base import BaseModel


class DropboxIntegration(BaseModel):
    """
    Store Dropbox OAuth credentials and integration settings for each owner
    """
    
    # Link to owner
    owner_id = Column(Integer, ForeignKey("owner.id"), nullable=False, unique=True)
    
    # OAuth credentials (encrypted)
    access_token = Column(Text, nullable=False)  # Will encrypt this
    refresh_token = Column(Text, nullable=True)  # For long-lived tokens
    
    # Dropbox account info
    account_id = Column(String(255), nullable=True)
    email = Column(String(255), nullable=True)
    name = Column(String(255), nullable=True)
    
    # Integration settings
    sync_enabled = Column(Boolean, default=True)
    sync_folder_path = Column(String(500), default="/CA_Documents")  # Root folder to sync
    auto_sync = Column(<PERSON>olean, default=True)
    sync_frequency_minutes = Column(Integer, default=15)  # How often to sync
    
    # Sync status
    last_sync_cursor = Column(Text, nullable=True)  # For delta syncing
    last_sync_at = Column(String(50), nullable=True)  # ISO timestamp
    sync_status = Column(String(20), default="pending")  # pending, syncing, completed, error
    last_error = Column(Text, nullable=True)
    
    # File filtering
    allowed_extensions = Column(Text, default="pdf,docx,xlsx,txt,csv")  # Comma-separated
    max_file_size_mb = Column(Integer, default=50)
    
    # Relationships
    owner = relationship("Owner", back_populates="dropbox_integration")
    
    def __repr__(self):
        return f"<DropboxIntegration(owner_id={self.owner_id}, email='{self.email}', sync_enabled={self.sync_enabled})>"
    
    def get_allowed_extensions_list(self):
        """Get allowed extensions as a list"""
        if self.allowed_extensions:
            return [ext.strip().lower() for ext in self.allowed_extensions.split(",")]
        return []
    
    def is_file_allowed(self, filename: str) -> bool:
        """Check if a file is allowed based on extension and size"""
        if not filename:
            return False
        
        # Check extension
        file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
        allowed_exts = self.get_allowed_extensions_list()
        
        if allowed_exts and file_ext not in allowed_exts:
            return False
        
        return True
