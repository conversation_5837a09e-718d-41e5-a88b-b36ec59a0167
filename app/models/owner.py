"""
Owner/User model for CA firm owners
"""
from sqlalchemy import Column, String, Boolean, Text
from sqlalchemy.orm import relationship
from .base import BaseModel


class Owner(BaseModel):
    """
    CA Firm Owner model - represents the firm owners who manage the system
    """
    
    # Basic info
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(255), nullable=False)
    firm_name = Column(String(255), nullable=False)
    
    # Authentication
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    
    # Contact info
    phone = Column(String(20), nullable=True)
    address = Column(Text, nullable=True)
    
    # System settings
    timezone = Column(String(50), default="UTC")
    
    # Relationships
    dropbox_integration = relationship("DropboxIntegration", back_populates="owner", uselist=False)
    documents = relationship("Document", back_populates="owner", cascade="all, delete-orphan")
    sync_jobs = relationship("SyncJob", back_populates="owner", cascade="all, delete-orphan")
    clients = relationship("Client", back_populates="owner", cascade="all, delete-orphan")
    # api_keys = relationship("APIKey", back_populates="owner")  # Will add later
    
    def __repr__(self):
        return f"<Owner(email='{self.email}', firm='{self.firm_name}')>"
