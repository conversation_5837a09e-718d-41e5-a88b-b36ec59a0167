"""
Sync job model for tracking file synchronization operations
"""
from sqlalchemy import Column, String, Boolean, Text, Integer, ForeignKey, DateTime, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from .base import BaseModel
from datetime import datetime


class SyncJob(BaseModel):
    """
    Track individual file sync operations and their progress
    """
    __tablename__ = "sync_jobs"

    # Job identification
    job_id = Column(String(36), nullable=False, unique=True, index=True)  # UUID for job tracking
    job_type = Column(String(50), nullable=False)  # full_sync, incremental_sync, single_file
    
    # Ownership and relationships
    owner_id = Column(Integer, ForeignKey("owner.id"), nullable=False, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=True, index=True)  # NULL for batch jobs
    
    # Job status and progress
    status = Column(String(50), default="pending", nullable=False)  # pending, running, completed, failed, cancelled
    progress_percentage = Column(Integer, default=0, nullable=False)
    
    # Timing information
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    estimated_completion = Column(DateTime, nullable=True)
    
    # Processing steps
    step_current = Column(String(100), nullable=True)  # Current processing step
    step_total = Column(Integer, default=1, nullable=False)
    step_completed = Column(Integer, default=0, nullable=False)
    
    # File information (for batch jobs)
    files_total = Column(Integer, default=0, nullable=False)
    files_processed = Column(Integer, default=0, nullable=False)
    files_failed = Column(Integer, default=0, nullable=False)
    files_skipped = Column(Integer, default=0, nullable=False)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)  # Detailed error information
    retry_count = Column(Integer, default=0, nullable=False)
    max_retries = Column(Integer, default=3, nullable=False)
    
    # Job configuration
    job_config = Column(JSON, nullable=True)  # Job-specific configuration
    
    # Results
    result_summary = Column(JSON, nullable=True)  # Summary of job results
    
    # Relationships
    owner = relationship("Owner", back_populates="sync_jobs")
    document = relationship("Document", back_populates="sync_jobs")
    
    def __repr__(self):
        return f"<SyncJob(id={self.id}, job_id='{self.job_id}', type='{self.job_type}', status='{self.status}')>"
    
    @property
    def duration_seconds(self):
        """Get job duration in seconds"""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.utcnow()
        return (end_time - self.started_at).total_seconds()
    
    @property
    def is_running(self):
        """Check if job is currently running"""
        return self.status in ["pending", "running"]
    
    @property
    def is_completed(self):
        """Check if job has completed (successfully or with failure)"""
        return self.status in ["completed", "failed", "cancelled"]
    
    @property
    def success_rate(self):
        """Calculate success rate for batch jobs"""
        if self.files_total == 0:
            return 0
        return (self.files_processed / self.files_total) * 100
    
    def start_job(self):
        """Mark job as started"""
        self.status = "running"
        self.started_at = datetime.utcnow()
        self.progress_percentage = 0
    
    def update_progress(self, percentage: int, current_step: str = None):
        """Update job progress"""
        self.progress_percentage = min(100, max(0, percentage))
        if current_step:
            self.step_current = current_step
    
    def update_file_progress(self, processed: int = None, failed: int = None, skipped: int = None):
        """Update file processing counts"""
        if processed is not None:
            self.files_processed = processed
        if failed is not None:
            self.files_failed = failed
        if skipped is not None:
            self.files_skipped = skipped
        
        # Update overall progress
        total_handled = self.files_processed + self.files_failed + self.files_skipped
        if self.files_total > 0:
            self.progress_percentage = min(100, int((total_handled / self.files_total) * 100))
    
    def complete_job(self, success: bool = True, summary: dict = None):
        """Mark job as completed"""
        self.status = "completed" if success else "failed"
        self.completed_at = datetime.utcnow()
        self.progress_percentage = 100 if success else self.progress_percentage
        
        if summary:
            self.result_summary = summary
    
    def fail_job(self, error_message: str, error_details: dict = None):
        """Mark job as failed"""
        self.status = "failed"
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        self.retry_count += 1
        
        if error_details:
            self.error_details = error_details
    
    def can_retry(self):
        """Check if job can be retried"""
        return self.retry_count < self.max_retries and self.status == "failed"
