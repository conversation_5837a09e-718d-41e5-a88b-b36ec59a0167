"""
Document model for storing file metadata and sync information
"""
from sqlalchemy import Column, String, <PERSON>olean, Text, Integer, ForeignKey, DateTime, BigInteger, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from .base import BaseModel
from datetime import datetime


class Document(BaseModel):
    """
    Store metadata for documents synced from Dropbox
    """
    __tablename__ = "documents"

    # Ownership
    owner_id = Column(Integer, ForeignKey("owner.id"), nullable=False, index=True)
    
    # Dropbox file information
    dropbox_file_id = Column(String(255), nullable=False, index=True)  # Dropbox file ID
    dropbox_path = Column(String(1000), nullable=False, index=True)    # Full path in Dropbox
    dropbox_rev = Column(String(255), nullable=True)                   # Dropbox revision ID
    
    # File metadata
    filename = Column(String(500), nullable=False)
    file_extension = Column(String(10), nullable=True)
    file_size_bytes = Column(BigInteger, nullable=True)
    mime_type = Column(String(100), nullable=True)
    
    # File hashes for change detection
    content_hash = Column(String(64), nullable=True)  # SHA-256 hash of content
    dropbox_content_hash = Column(String(64), nullable=True)  # Dropbox's content hash
    
    # Timestamps
    dropbox_created_at = Column(DateTime, nullable=True)
    dropbox_modified_at = Column(DateTime, nullable=True)
    last_synced_at = Column(DateTime, nullable=True)
    
    # Processing status
    is_processed = Column(Boolean, default=False, nullable=False)
    text_extracted = Column(Boolean, default=False, nullable=False)
    embeddings_generated = Column(Boolean, default=False, nullable=False)
    searchable = Column(Boolean, default=False, nullable=False)
    
    # Content storage
    extracted_text = Column(Text, nullable=True)  # Extracted text content
    text_summary = Column(Text, nullable=True)    # AI-generated summary
    
    # Processing metadata
    processing_metadata = Column(JSON, nullable=True)  # Store processing info as JSON
    
    # Sync tracking
    sync_status = Column(String(50), default="pending", nullable=False)  # pending, syncing, completed, failed, deleted
    sync_error = Column(Text, nullable=True)
    sync_attempts = Column(Integer, default=0, nullable=False)
    
    # Relationships
    owner = relationship("Owner", back_populates="documents")
    sync_jobs = relationship("SyncJob", back_populates="document", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Document(id={self.id}, filename='{self.filename}', owner_id={self.owner_id}, status='{self.sync_status}')>"
    
    @property
    def file_type_category(self):
        """Get general file category based on extension"""
        if not self.file_extension:
            return "unknown"
        
        ext = self.file_extension.lower().lstrip('.')
        
        categories = {
            'document': ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'],
            'spreadsheet': ['xls', 'xlsx', 'csv', 'ods'],
            'presentation': ['ppt', 'pptx', 'odp'],
            'image': ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'],
            'archive': ['zip', 'rar', '7z', 'tar', 'gz'],
            'audio': ['mp3', 'wav', 'flac', 'aac', 'm4a'],
            'video': ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv']
        }
        
        for category, extensions in categories.items():
            if ext in extensions:
                return category
        
        return "other"
    
    @property
    def is_text_extractable(self):
        """Check if text can be extracted from this file type"""
        extractable_types = ['document', 'spreadsheet', 'presentation']
        return self.file_type_category in extractable_types
    
    def mark_processing_complete(self):
        """Mark document as fully processed"""
        self.is_processed = True
        self.searchable = self.text_extracted and self.embeddings_generated
        if self.sync_status == "syncing":
            self.sync_status = "completed"
    
    def mark_sync_failed(self, error_message: str):
        """Mark document sync as failed"""
        self.sync_status = "failed"
        self.sync_error = error_message
        self.sync_attempts += 1
