"""
Base model with common fields and utilities
"""
from sqlalchemy import <PERSON>um<PERSON>, Integer, DateTime, func
from sqlalchemy.ext.declarative import declared_attr
from ..core.database import Base


class BaseModel(Base):
    """
    Base model class with common fields for all tables
    """
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    @declared_attr
    def __tablename__(cls):
        # Generate table name from class name (convert CamelCase to snake_case)
        import re
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()
