{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                    <h2 class="card-title">Register CA Firm</h2>
                    <p class="text-muted">Create your document management account</p>
                </div>

                <!-- Alert container -->
                <div id="alert-container"></div>

                <form id="registerForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="fullName" class="form-label">
                                <i class="fas fa-user me-1"></i>Full Name
                            </label>
                            <input type="text" class="form-control" id="fullName" required placeholder="Your full name">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="firmName" class="form-label">
                                <i class="fas fa-building me-1"></i>Firm Name
                            </label>
                            <input type="text" class="form-control" id="firmName" required placeholder="CA firm name">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email Address
                        </label>
                        <input type="email" class="form-control" id="email" required placeholder="Enter your email">
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-1"></i>Phone Number
                            </label>
                            <input type="tel" class="form-control" id="phone" placeholder="Your phone number">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="timezone" class="form-label">
                                <i class="fas fa-clock me-1"></i>Timezone
                            </label>
                            <select class="form-control" id="timezone">
                                <option value="UTC">UTC</option>
                                <option value="America/New_York">Eastern Time</option>
                                <option value="America/Chicago">Central Time</option>
                                <option value="America/Denver">Mountain Time</option>
                                <option value="America/Los_Angeles">Pacific Time</option>
                                <option value="Asia/Kolkata">India Standard Time</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">
                            <i class="fas fa-map-marker-alt me-1"></i>Business Address
                        </label>
                        <textarea class="form-control" id="address" rows="2"
                            placeholder="Your business address"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-1"></i>Password
                        </label>
                        <input type="password" class="form-control" id="password" required
                            placeholder="Choose a strong password" minlength="8">
                        <div class="form-text">Password must be at least 8 characters long.</div>
                    </div>

                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">
                            <i class="fas fa-lock me-1"></i>Confirm Password
                        </label>
                        <input type="password" class="form-control" id="confirmPassword" required
                            placeholder="Confirm your password">
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="termsCheck" required>
                        <label class="form-check-label" for="termsCheck">
                            I agree to the <a href="#" class="text-primary">Terms of Service</a>
                            and <a href="#" class="text-primary">Privacy Policy</a>
                        </label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="registerBtn">
                            <i class="fas fa-user-plus me-2"></i>Create Account
                        </button>
                    </div>
                </form>

                <hr class="my-4">

                <div class="text-center">
                    <p class="mb-0">Already have an account?</p>
                    <a href="/api/owner/dashboard/login" class="btn btn-outline-primary mt-2">
                        <i class="fas fa-sign-in-alt me-2"></i>Login to Your Account
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.getElementById('registerForm').addEventListener('submit', async function (e) {
        e.preventDefault();

        const registerBtn = document.getElementById('registerBtn');
        const originalText = registerBtn.innerHTML;

        // Validate passwords match
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (password !== confirmPassword) {
            showAlert('Passwords do not match!', 'danger');
            return;
        }

        // Show loading state
        registerBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Account...';
        registerBtn.disabled = true;

        const formData = {
            email: document.getElementById('email').value,
            full_name: document.getElementById('fullName').value,
            firm_name: document.getElementById('firmName').value,
            phone: document.getElementById('phone').value || null,
            address: document.getElementById('address').value || null,
            timezone: document.getElementById('timezone').value,
            password: password
        };

        try {
            const response = await fetch('/api/owner/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (response.ok) {
                showAlert('Account created successfully! Redirecting to login...', 'success');

                // Redirect to login page
                setTimeout(() => {
                    window.location.href = '/api/owner/dashboard/login';
                }, 2000);
            } else {
                showAlert(data.detail || 'Registration failed. Please try again.', 'danger');
            }
        } catch (error) {
            console.error('Registration error:', error);
            showAlert('Registration failed. Please try again.', 'danger');
        } finally {
            // Restore button state
            registerBtn.innerHTML = originalText;
            registerBtn.disabled = false;
        }
    });

    // Real-time password confirmation validation
    document.getElementById('confirmPassword').addEventListener('input', function () {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;

        if (confirmPassword && password !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
    });
</script>
{% endblock %}