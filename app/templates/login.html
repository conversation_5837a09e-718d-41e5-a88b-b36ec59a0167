{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-user-shield fa-3x text-primary mb-3"></i>
                    <h2 class="card-title">CA Firm Login</h2>
                    <p class="text-muted">Access your document management portal</p>
                </div>

                <!-- Alert container -->
                <div id="alert-container"></div>

                <form id="loginForm">
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email Address
                        </label>
                        <input type="email" class="form-control form-control-lg" id="email" required
                            placeholder="Enter your email">
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-1"></i>Password
                        </label>
                        <input type="password" class="form-control form-control-lg" id="password" required
                            placeholder="Enter your password">
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            Remember me
                        </label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </div>
                </form>

                <hr class="my-4">

                <div class="text-center">
                    <p class="mb-2">Don't have an account?</p>
                    <a href="/api/owner/dashboard/register" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus me-2"></i>Register New Firm
                    </a>
                </div>

                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Need help? Contact <NAME_EMAIL>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.getElementById('loginForm').addEventListener('submit', async function (e) {
        e.preventDefault();

        const loginBtn = document.getElementById('loginBtn');
        const originalText = loginBtn.innerHTML;

        // Show loading state
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';
        loginBtn.disabled = true;

        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        try {
            const response = await fetch('/api/owner/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: email,
                    password: password
                })
            });

            const data = await response.json();

            if (response.ok) {
                // Store token and owner data
                localStorage.setItem('access_token', data.access_token);
                localStorage.setItem('owner_data', JSON.stringify(data.owner));

                showAlert('Login successful! Redirecting to dashboard...', 'success');

                console.log('Login successful, redirecting to dashboard...');
                console.log('Token stored:', data.access_token);

                // Small delay to show success message, then redirect
                setTimeout(() => {
                    console.log('Redirecting now...');
                    window.location.href = '/api/owner/dashboard/';
                }, 500);
            } else {
                console.error('Login failed:', data);
                showAlert(data.detail || 'Login failed. Please check your credentials.', 'danger');
            }
        } catch (error) {
            console.error('Login error:', error);
            showAlert('Login failed. Please try again.', 'danger');
        } finally {
            // Restore button state
            loginBtn.innerHTML = originalText;
            loginBtn.disabled = false;
        }
    });

    // Check if user is already logged in
    window.addEventListener('load', function () {
        const token = localStorage.getItem('access_token');
        if (token) {
            // Verify token is still valid
            fetch('/api/owner/auth/verify-token', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            })
                .then(response => {
                    if (response.ok) {
                        // Redirect to dashboard if already logged in
                        window.location.href = '/api/owner/dashboard/';
                    } else {
                        // Remove invalid token
                        localStorage.removeItem('access_token');
                        localStorage.removeItem('owner_data');
                    }
                })
                .catch(error => {
                    console.error('Token verification error:', error);
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('owner_data');
                });
        }
    });
</script>
{% endblock %}