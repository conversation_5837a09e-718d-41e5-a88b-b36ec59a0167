{% extends "base.html" %}

{% block content %}
<!-- Professional Header -->
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center py-3 px-4 bg-white border-bottom">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center"
                        style="width: 48px; height: 48px;">
                        <i class="fas fa-briefcase text-white fa-lg"></i>
                    </div>
                </div>
                <div>
                    <h4 class="mb-0 fw-bold text-dark" id="welcome-message">Welcome back!</h4>
                    <small class="text-muted" id="firm-info">Loading...</small>
                </div>
            </div>
            <div class="d-flex align-items-center gap-3">
                <div class="text-end d-none d-md-block">
                    <div class="small text-muted">Last sync</div>
                    <div class="fw-semibold text-dark" id="last-sync-time">Just now</div>
                </div>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"
                        data-bs-toggle="dropdown">
                        <i class="fas fa-cog me-1"></i>Settings
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="refreshAllData()"><i
                                    class="fas fa-sync me-2"></i>Refresh Data</a></li>
                        <li><a class="dropdown-item" href="#" onclick="loadDashboardStats()"><i
                                    class="fas fa-chart-bar me-2"></i>Load Stats</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-download me-2"></i>Export Files</a></li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li><a class="dropdown-item" href="#" onclick="logout()"><i
                                    class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alert container -->
<div id="alert-container"></div>

<!-- Compact Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-primary bg-opacity-10 p-2 me-3">
                        <i class="fas fa-file-alt fa-lg text-primary"></i>
                    </div>
                    <div>
                        <h4 class="mb-0 fw-bold" id="documents-count">0</h4>
                        <small class="text-muted">Total Files</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-success bg-opacity-10 p-2 me-3">
                        <i class="fas fa-check-circle fa-lg text-success"></i>
                    </div>
                    <div>
                        <h4 class="mb-0 fw-bold" id="processed-count">0</h4>
                        <small class="text-muted">Processed</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-warning bg-opacity-10 p-2 me-3">
                        <i class="fas fa-sync-alt fa-lg text-warning"></i>
                    </div>
                    <div>
                        <h4 class="mb-0 fw-bold" id="syncing-count">0</h4>
                        <small class="text-muted">Syncing</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-info bg-opacity-10 p-2 me-3">
                        <i class="fas fa-search fa-lg text-info"></i>
                    </div>
                    <div>
                        <h4 class="mb-0 fw-bold" id="searchable-count">0</h4>
                        <small class="text-muted">Searchable</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dropbox Connection Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                                <i class="fab fa-dropbox fa-2x text-primary"></i>
                            </div>
                            <div>
                                <h5 class="mb-1">Dropbox Integration</h5>
                                <div class="d-flex align-items-center">
                                    <i id="dropbox-status-icon" class="fas fa-clock text-muted me-2"></i>
                                    <span class="badge bg-secondary" id="dropbox-status">
                                        Checking...
                                    </span>
                                    <span class="text-muted ms-2" id="dropbox-account-info">
                                        Status loading...
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <!-- Buttons shown when NOT connected -->
                        <div id="dropbox-not-connected-buttons" class="d-none">
                            <button class="btn btn-primary" onclick="connectDropbox()">
                                <i class="fab fa-dropbox me-1"></i>Connect Dropbox
                            </button>
                        </div>

                        <!-- Buttons shown when connected -->
                        <div id="dropbox-connected-buttons" class="d-none">
                            <button class="btn btn-outline-primary me-2" onclick="testDropboxConnection()">
                                <i class="fas fa-wifi me-1"></i>Test
                            </button>
                            <button class="btn btn-outline-success me-2" onclick="browseDropboxFiles()">
                                <i class="fas fa-folder me-1"></i>Browse Files
                            </button>
                            <button class="btn btn-outline-danger" onclick="disconnectDropbox()">
                                <i class="fas fa-unlink me-1"></i>Disconnect
                            </button>
                        </div>

                        <!-- Loading state -->
                        <div id="dropbox-loading-buttons">
                            <button class="btn btn-outline-secondary" disabled>
                                <i class="fas fa-spinner fa-spin me-1"></i>Checking...
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CA_Firm Files for Embedding -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm" style="border: 1px solid #dee2e6; border-radius: 0.5rem;">
            <div class="card-header bg-light py-3"
                style="border-bottom: 1px solid #dee2e6; border-radius: 0.5rem 0.5rem 0 0;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 fw-semibold">
                            <i class="fas fa-file-alt me-2 text-success"></i>
                            CA_Firm Files - Ready for AI Embedding
                        </h5>
                        <p class="text-muted mb-0 small">Select files from your CA_Firm folder to process for AI search
                        </p>
                    </div>
                    <button class="btn btn-outline-primary btn-sm" onclick="loadCAFirmFiles()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh Files
                    </button>
                </div>
            </div>

            <!-- Search and Filter Controls -->
            <div class="card-body border-bottom" style="background-color: #f8f9fa;">
                <div class="row align-items-center">
                    <div class="col-md-5">
                        <div class="input-group">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-search text-muted"></i>
                            </span>
                            <input type="text" id="search-input" class="form-control border-start-0"
                                placeholder="Search files by name..." onkeyup="performSearch()">
                        </div>
                    </div>
                    <div class="col-md-5">
                        <select id="file-type-filter" class="form-select" onchange="performSearch()">
                            <option value="">All File Types</option>
                            <option value="pdf">PDF Files</option>
                            <option value="xls">Excel Files (XLS)</option>
                            <option value="xlsx">Excel Files (XLSX)</option>
                            <option value="csv">CSV Files</option>
                            <option value="doc">Word Files (DOC)</option>
                            <option value="docx">Word Files (DOCX)</option>
                            <option value="txt">Text Files</option>
                        </select>
                    </div>
                    <div class="col-md-2 text-end">
                        <button class="btn btn-outline-secondary" onclick="clearSearch()">
                            <i class="fas fa-times me-1"></i>Clear
                        </button>
                    </div>
                </div>
            </div>

            <div class="card-body" style="border-radius: 0 0 0.5rem 0.5rem;">
                <div id="ca-firm-files-container">
                    <div class="text-center py-4" id="files-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading files...</span>
                        </div>
                        <p class="text-muted mt-2">Loading files from CA_Firm folder...</p>
                    </div>
                </div>

                <div class="mt-3 d-none border-top pt-3" id="file-actions" style="border-color: #dee2e6 !important;">
                    <div class="row">
                        <div class="col-md-6">
                            <button class="btn btn-outline-secondary me-2" onclick="selectAllFiles()">
                                <i class="fas fa-check-square me-1"></i>Select All
                            </button>
                            <button class="btn btn-outline-secondary" onclick="deselectAllFiles()">
                                <i class="fas fa-square me-1"></i>Deselect All
                            </button>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <button class="btn btn-success" onclick="startEmbeddingProcess()" id="embedding-btn"
                                disabled>
                                <i class="fas fa-brain me-1"></i>Start Embedding Process
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Processing Status -->
<div class="row mb-4 d-none" id="processing-status-section">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-bottom-0 pb-0">
                <h5 class="mb-1">
                    <i class="fas fa-cogs me-2 text-info"></i>
                    Processing Status
                </h5>
                <p class="text-muted mb-0">AI processing progress for your documents</p>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="fw-medium" id="current-job-title">No active jobs</span>
                            <span class="text-muted" id="job-progress-text">0%</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 0%"
                                id="job-progress-bar"></div>
                        </div>
                    </div>
                </div>

                <div class="row text-center">
                    <div class="col-md-3">
                        <small class="text-muted d-block">Files Processed</small>
                        <span class="fw-bold" id="files-processed">0</span>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted d-block">Total Files</small>
                        <span class="fw-bold" id="total-files">0</span>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted d-block">Time Remaining</small>
                        <span class="fw-bold" id="time-remaining">--</span>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-warning btn-sm" onclick="pauseProcessing()">
                            <i class="fas fa-pause me-1"></i>Pause
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-bottom-0 pb-0">
                <h5 class="mb-1">
                    <i class="fas fa-bolt me-2 text-primary"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <button class="btn btn-outline-primary" onclick="showSearchModal()">
                        <i class="fas fa-search me-2"></i>Search Documents
                    </button>
                    <button class="btn btn-outline-info" onclick="showSettingsModal()">
                        <i class="fas fa-cog me-2"></i>Sync Settings
                    </button>
                    <button class="btn btn-outline-success" onclick="showClientManagement()">
                        <i class="fas fa-users me-2"></i>Manage Clients
                    </button>
                    <a href="/docs" class="btn btn-outline-secondary" target="_blank">
                        <i class="fas fa-book me-2"></i>API Documentation
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-bottom-0 pb-0">
                <h5 class="mb-1">
                    <i class="fas fa-chart-line me-2 text-success"></i>System Health
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center p-3 bg-success bg-opacity-10 rounded">
                            <i class="fas fa-database fa-2x text-success mb-2"></i>
                            <small class="d-block text-muted">Database</small>
                            <span class="badge bg-success">Online</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-success bg-opacity-10 rounded">
                            <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                            <small class="d-block text-muted">Security</small>
                            <span class="badge bg-success">Active</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-warning bg-opacity-10 rounded">
                            <i class="fas fa-tasks fa-2x text-warning mb-2"></i>
                            <small class="d-block text-muted">Background</small>
                            <span class="badge bg-warning">Pending</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-3 bg-info bg-opacity-10 rounded">
                            <i class="fas fa-robot fa-2x text-info mb-2"></i>
                            <small class="d-block text-muted">AI Engine</small>
                            <span class="badge bg-info">Ready</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global state
    let selectedFolders = new Set();
    let allFolders = [];

    // Load dashboard stats on page load
    window.addEventListener('load', async function () {
        console.log('Dashboard page loaded');

        // Check if user has valid token first
        const token = localStorage.getItem('access_token');
        if (!token) {
            console.log('No token found, redirecting to login...');
            setTimeout(() => {
                window.location.href = '/api/owner/dashboard/login';
            }, 100);
            return;
        }

        console.log('Dashboard loading with token:', token.substring(0, 20) + '...');

        // Verify token is valid before proceeding
        try {
            const verifyResponse = await fetch('/api/owner/auth/verify-token', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!verifyResponse.ok) {
                console.log('Token verification failed, redirecting to login...');
                localStorage.removeItem('access_token');
                localStorage.removeItem('owner_data');
                setTimeout(() => {
                    window.location.href = '/api/owner/dashboard/login';
                }, 100);
                return;
            }

            console.log('Token verified successfully');
            await loadDashboardStats();
            await checkDropboxStatus();
            await loadCAFirmFiles();
        } catch (error) {
            console.error('Token verification error:', error);
            localStorage.removeItem('access_token');
            localStorage.removeItem('owner_data');
            setTimeout(() => {
                window.location.href = '/api/owner/dashboard/login';
            }, 100);
        }
    });

    async function loadDashboardStats() {
        try {
            const token = localStorage.getItem('access_token');
            if (!token) {
                console.log('No token in loadDashboardStats');
                return;
            }

            const response = await fetch('/api/owner/dashboard/api/stats', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const stats = await response.json();
                updateDashboardStats(stats);
                console.log('Dashboard stats loaded successfully');
            } else if (response.status === 401) {
                console.log('Token expired while loading stats');
            } else {
                console.log('Failed to load stats:', response.status);
            }
        } catch (error) {
            console.error('Error loading dashboard stats:', error);
        }
    }

    function updateDashboardStats(stats) {
        console.log('Updating dashboard stats with:', stats);

        // Update welcome message and firm info
        if (stats.owner_info) {
            const welcomeMessage = document.getElementById('welcome-message');
            const firmInfo = document.getElementById('firm-info');

            // Store owner ID globally for client management
            if (stats.owner_info.id) {
                currentOwnerId = stats.owner_info.id;
                console.log('Stored current owner ID:', currentOwnerId);
            }

            if (welcomeMessage && stats.owner_info.name) {
                welcomeMessage.textContent = `Welcome back, ${stats.owner_info.name}!`;
                console.log('Updated welcome message:', stats.owner_info.name);
            }

            if (firmInfo && stats.owner_info.firm) {
                firmInfo.innerHTML = `<i class="fas fa-building me-1"></i>${stats.owner_info.firm}`;
                console.log('Updated firm info:', stats.owner_info.firm);
            }
        }

        // Update stats cards with real data
        if (stats.system_stats) {
            const documentsCount = document.getElementById('documents-count');
            const processedCount = document.getElementById('processed-count');
            const syncingCount = document.getElementById('syncing-count');
            const searchableCount = document.getElementById('searchable-count');

            if (documentsCount) documentsCount.textContent = stats.system_stats.documents_count || 0;
            if (processedCount) processedCount.textContent = stats.system_stats.processed_count || 0;
            if (syncingCount) syncingCount.textContent = stats.system_stats.syncing_count || 0;
            if (searchableCount) searchableCount.textContent = stats.system_stats.searchable_count || 0;
        }
    }

    // Dropbox integration functions
    async function checkDropboxStatus() {
        try {
            const token = localStorage.getItem('access_token');
            if (!token) return;

            const response = await fetch('/api/owner/dropbox/status', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const statusBadge = document.getElementById('dropbox-status-badge');
            const accountInfo = document.getElementById('dropbox-account-info');

            if (response.ok) {
                const status = await response.json();
                if (status) {
                    statusBadge.innerHTML = '<i class="fas fa-check me-1"></i>Connected';
                    statusBadge.className = 'badge bg-success';
                    if (status.email) {
                        accountInfo.innerHTML = `<i class="fas fa-user me-1"></i>${status.email} • <i class="fas fa-clock me-1"></i>Last sync: ${status.last_sync || 'Never'}`;
                    }
                } else {
                    statusBadge.innerHTML = '<i class="fas fa-times me-1"></i>Not Connected';
                    statusBadge.className = 'badge bg-warning';
                    accountInfo.innerHTML = '<i class="fas fa-info-circle me-1"></i>Click "Browse Files" to connect';
                }
            } else {
                statusBadge.innerHTML = '<i class="fas fa-exclamation me-1"></i>Error';
                statusBadge.className = 'badge bg-danger';
                accountInfo.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Connection error';
            }
        } catch (error) {
            console.error('Error checking Dropbox status:', error);
            const statusBadge = document.getElementById('dropbox-status-badge');
            const accountInfo = document.getElementById('dropbox-account-info');
            statusBadge.innerHTML = '<i class="fas fa-exclamation me-1"></i>Error';
            statusBadge.className = 'badge bg-danger';
            accountInfo.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Connection error';
        }
    }

    async function connectDropbox() {
        try {
            const token = localStorage.getItem('access_token');
            if (!token) {
                showAlert('Please login first', 'error');
                return;
            }

            const response = await fetch('/api/owner/dropbox/auth-url', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                showDropboxAuthModal(data.auth_url, data.instructions);
            } else {
                const error = await response.text();
                showAlert(`Failed to start Dropbox connection: ${error}`, 'error');
            }
        } catch (error) {
            console.error('Error connecting to Dropbox:', error);
            showAlert('Failed to connect to Dropbox', 'error');
        }
    }

    async function disconnectDropbox() {
        try {
            // Show confirmation dialog
            if (!confirm('Are you sure you want to disconnect your Dropbox account? This will remove access to your files and stop syncing.')) {
                return;
            }

            const token = localStorage.getItem('access_token');
            if (!token) {
                showAlert('Please login first', 'error');
                return;
            }

            showAlert('Disconnecting Dropbox...', 'info');

            const response = await fetch('/api/owner/dropbox/disconnect', {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const result = await response.json();
                showAlert('Dropbox disconnected successfully!', 'success');

                // Update UI to show disconnected state
                const statusElement = document.getElementById('dropbox-status');
                const statusIcon = document.getElementById('dropbox-status-icon');
                const accountInfo = document.getElementById('dropbox-account-info');

                statusElement.textContent = 'Disconnected';
                statusElement.className = 'badge bg-danger';
                statusIcon.className = 'fas fa-exclamation-circle text-danger me-2';
                accountInfo.innerHTML = '<i class="fas fa-times me-1"></i>Please connect to Dropbox';

                // Update buttons to show connect option
                updateDropboxButtons(false);

                // Refresh dashboard stats
                await loadDashboardStats();
            } else {
                const error = await response.json();
                showAlert(error.detail || 'Failed to disconnect Dropbox', 'error');
            }
        } catch (error) {
            console.error('Error disconnecting Dropbox:', error);
            showAlert('Failed to disconnect Dropbox', 'error');
        }
    }

    // New folder selection functionality
    async function loadDropboxFolders() {
        try {
            const token = localStorage.getItem('access_token');
            if (!token) return;

            // Show loading state
            const container = document.getElementById('folder-selection-container');
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="text-muted mt-2">Loading folders and calculating file counts...</p>
                </div>
            `;

            // Load both folder list and statistics
            const [foldersResponse, statsResponse] = await Promise.all([
                fetch('/api/owner/dropbox/folders', {
                    headers: { 'Authorization': `Bearer ${token}` }
                }),
                fetch('/api/owner/dropbox/folders/stats', {
                    headers: { 'Authorization': `Bearer ${token}` }
                })
            ]);

            if (foldersResponse.ok && statsResponse.ok) {
                const foldersData = await foldersResponse.json();
                const statsData = await statsResponse.json();

                allFolders = foldersData.items.filter(item => item.is_folder);
                displayFolderSelection(allFolders, statsData.folder_stats);
                document.getElementById('folder-actions').classList.remove('d-none');
            } else {
                const container = document.getElementById('folder-selection-container');
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <p class="text-muted">Please connect to Dropbox first to see your folders</p>
                        <button class="btn btn-primary" onclick="browseDropboxFiles()">
                            <i class="fab fa-dropbox me-1"></i>Connect Dropbox
                        </button>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading folders:', error);
            const container = document.getElementById('folder-selection-container');
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                    <p class="text-muted">Failed to load folders</p>
                    <button class="btn btn-outline-primary" onclick="loadDropboxFolders()">
                        <i class="fas fa-sync-alt me-1"></i>Retry
                    </button>
                </div>
            `;
        }
    }

    function displayFolderSelection(folders, folderStats = {}) {
        const container = document.getElementById('folder-selection-container');

        if (folders.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-folder-open fa-2x text-muted mb-2"></i>
                    <p class="text-muted">No folders found in your Dropbox</p>
                </div>
            `;
            return;
        }

        // Helper function to format file size
        function formatSize(bytes) {
            if (!bytes || bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // Get real file count and size for each folder
        const folderHTML = folders.map(folder => {
            const stats = folderStats[folder.path] || {
                file_count: 0,
                folder_count: 0,
                total_size: 0,
                total_items: 0
            };

            const totalItems = stats.total_items;
            const fileCount = stats.file_count;
            const folderCount = stats.folder_count;
            const sizeFormatted = formatSize(stats.total_size);

            return `
                <div class="row align-items-center py-3 border-bottom">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="${folder.path}" 
                                   id="folder-${folder.path.replace(/[^a-zA-Z0-9]/g, '')}"
                                   onchange="toggleFolderSelection('${folder.path}')">
                            <label class="form-check-label" for="folder-${folder.path.replace(/[^a-zA-Z0-9]/g, '')}">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-folder text-warning me-2"></i>
                                    <div>
                                        <div class="fw-medium">${folder.name}</div>
                                        <small class="text-muted">${folder.path}</small>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="d-flex flex-column">
                            <span class="badge bg-info mb-1">${totalItems} items</span>
                            <small class="text-muted">${fileCount} files, ${folderCount} folders</small>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <small class="text-muted">${sizeFormatted}</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <button class="btn btn-outline-primary btn-sm" onclick="processSingleFolder('${folder.path}')" 
                                ${totalItems === 0 ? 'disabled' : ''}>
                            <i class="fas fa-cogs me-1"></i>Process
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = `
            <div class="border rounded p-3">
                <div class="row align-items-center py-2 border-bottom bg-light">
                    <div class="col-md-6"><strong>Folder Name</strong></div>
                    <div class="col-md-2 text-center"><strong>Files</strong></div>
                    <div class="col-md-2 text-center"><strong>Size</strong></div>
                    <div class="col-md-2 text-center"><strong>Action</strong></div>
                </div>
                ${folderHTML}
            </div>
        `;
    }

    function toggleFolderSelection(folderPath) {
        if (selectedFolders.has(folderPath)) {
            selectedFolders.delete(folderPath);
        } else {
            selectedFolders.add(folderPath);
        }

        // Update process button
        const processBtn = document.getElementById('process-btn');
        if (selectedFolders.size > 0) {
            processBtn.innerHTML = `<i class="fas fa-cogs me-1"></i>Process ${selectedFolders.size} Selected Folder${selectedFolders.size > 1 ? 's' : ''}`;
            processBtn.disabled = false;
        } else {
            processBtn.innerHTML = '<i class="fas fa-cogs me-1"></i>Process Selected Folders';
            processBtn.disabled = true;
        }
    }

    function selectAllFolders() {
        allFolders.forEach(folder => {
            selectedFolders.add(folder.path);
            const checkbox = document.getElementById(`folder-${folder.path.replace(/[^a-zA-Z0-9]/g, '')}`);
            if (checkbox) checkbox.checked = true;
        });
        toggleFolderSelection(''); // Update UI
    }

    function deselectAllFolders() {
        selectedFolders.clear();
        allFolders.forEach(folder => {
            const checkbox = document.getElementById(`folder-${folder.path.replace(/[^a-zA-Z0-9]/g, '')}`);
            if (checkbox) checkbox.checked = false;
        });
        const processBtn = document.getElementById('process-btn');
        processBtn.innerHTML = '<i class="fas fa-cogs me-1"></i>Process Selected Folders';
        processBtn.disabled = true;
    }

    function processSelectedFolders() {
        if (selectedFolders.size === 0) {
            showAlert('Please select at least one folder to process', 'warning');
            return;
        }

        showAlert(`Processing ${selectedFolders.size} folder${selectedFolders.size > 1 ? 's' : ''} will be available with Celery integration!`, 'info');

        // Show processing status section
        document.getElementById('processing-status-section').classList.remove('d-none');

        // Mock progress simulation
        simulateProcessing();
    }

    function processSingleFolder(folderPath) {
        selectedFolders.clear();
        selectedFolders.add(folderPath);
        processSelectedFolders();
    }

    function simulateProcessing() {
        const jobTitle = document.getElementById('current-job-title');
        const progressText = document.getElementById('job-progress-text');
        const progressBar = document.getElementById('job-progress-bar');
        const filesProcessed = document.getElementById('files-processed');
        const totalFiles = document.getElementById('total-files');
        const timeRemaining = document.getElementById('time-remaining');

        jobTitle.textContent = `Processing ${selectedFolders.size} selected folder${selectedFolders.size > 1 ? 's' : ''}`;

        let progress = 0;
        const total = 150;
        totalFiles.textContent = total;

        const interval = setInterval(() => {
            progress += Math.random() * 5;
            if (progress > 100) progress = 100;

            const processed = Math.floor((progress / 100) * total);
            filesProcessed.textContent = processed;
            progressText.textContent = `${Math.floor(progress)}%`;
            progressBar.style.width = `${progress}%`;

            const remaining = Math.max(0, Math.floor((100 - progress) * 2));
            timeRemaining.textContent = remaining > 0 ? `${remaining} min` : 'Almost done';

            if (progress >= 100) {
                clearInterval(interval);
                showAlert('Folder processing completed successfully!', 'success');
                setTimeout(() => {
                    document.getElementById('processing-status-section').classList.add('d-none');
                }, 3000);
            }
        }, 1000);
    }

    // Placeholder functions for new features
    function showSearchModal() {
        showAlert('Document search feature coming with vector embeddings!', 'info');
    }

    function showSettingsModal() {
        showAlert('Sync settings configuration coming soon!', 'info');
    }

    function showClientManagement() {
        // Load and show the client management modal
        loadClientManagementModal();
    }

    function pauseProcessing() {
        showAlert('Processing paused! Resume feature coming with Celery integration.', 'warning');
    }

    async function testDropboxConnection() {
        try {
            const token = localStorage.getItem('access_token');
            if (!token) return;

            showAlert('Testing Dropbox connection...', 'info');

            const response = await fetch('/api/owner/dropbox/test-connection', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.connected) {
                    showAlert('Dropbox connection test successful!', 'success');
                } else {
                    showAlert(`Connection test failed: ${result.error}`, 'error');
                }
            } else {
                showAlert('Failed to test connection', 'error');
            }
        } catch (error) {
            console.error('Error testing connection:', error);
            showAlert('Connection test failed', 'error');
        }
    }

    async function browseDropboxFiles(folderPath = '') {
        // First check if user is connected, if not, initiate connection
        const token = localStorage.getItem('access_token');
        if (!token) {
            showAlert('Please login first', 'error');
            return;
        }

        // Check Dropbox connection status first
        try {
            const statusResponse = await fetch('/api/owner/dropbox/status', {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (!statusResponse.ok || !(await statusResponse.json())) {
                // Not connected, show connection modal
                connectDropbox();
                return;
            }
        } catch (error) {
            connectDropbox();
            return;
        }

        // Connected, now browse files
        try {
            showAlert('Loading files...', 'info');

            const url = `/api/owner/dropbox/folders${folderPath ? `?folder_path=${encodeURIComponent(folderPath)}` : ''}`;
            const response = await fetch(url, {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (response.ok) {
                const data = await response.json();
                displayFileExplorer(data);
            } else {
                const error = await response.text();
                showAlert(`Failed to browse files: ${error}`, 'error');
            }
        } catch (error) {
            console.error('Error browsing files:', error);
            showAlert('Failed to browse files', 'error');
        }
    }

    function displayFileExplorer(folderData) {
        // Remove existing modal if any
        const existingModal = document.getElementById('fileExplorerModal');
        if (existingModal) existingModal.remove();

        // Create breadcrumb navigation
        const pathParts = folderData.path ? folderData.path.split('/').filter(p => p) : [];
        const breadcrumbHtml = `
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-3">
                    <li class="breadcrumb-item">
                        <a href="#" onclick="browseDropboxFiles('')" class="text-decoration-none">
                            <i class="fab fa-dropbox me-1"></i>Root
                        </a>
                    </li>
                    ${pathParts.map((part, index) => {
            const path = '/' + pathParts.slice(0, index + 1).join('/');
            const isLast = index === pathParts.length - 1;
            return `<li class="breadcrumb-item ${isLast ? 'active' : ''}">
                        ${isLast ? `<span>${part}</span>` :
                    `<a href="#" onclick="browseDropboxFiles('${path}')" class="text-decoration-none">${part}</a>`}
                    </li>`;
        }).join('')}
                </ol>
            </nav>
        `;

        // Helper functions
        function getFileIcon(filename, isFolder) {
            if (isFolder) return 'fas fa-folder text-warning';
            const ext = filename.toLowerCase().split('.').pop();
            const iconMap = {
                'pdf': 'fas fa-file-pdf text-danger',
                'doc': 'fas fa-file-word text-primary', 'docx': 'fas fa-file-word text-primary',
                'xls': 'fas fa-file-excel text-success', 'xlsx': 'fas fa-file-excel text-success',
                'txt': 'fas fa-file-alt text-secondary', 'csv': 'fas fa-file-csv text-info',
                'jpg': 'fas fa-file-image text-info', 'jpeg': 'fas fa-file-image text-info',
                'png': 'fas fa-file-image text-info', 'gif': 'fas fa-file-image text-info',
                'zip': 'fas fa-file-archive text-dark', 'rar': 'fas fa-file-archive text-dark'
            };
            return iconMap[ext] || 'fas fa-file text-secondary';
        }

        function formatFileSize(bytes) {
            if (!bytes) return '';
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit'
            });
        }

        const modal = document.createElement('div');
        modal.id = 'fileExplorerModal';
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title"><i class="fab fa-dropbox me-2"></i>Dropbox File Explorer</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-4">
                        ${breadcrumbHtml}
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="fileSearch" 
                                           placeholder="Search files and folders..." onkeyup="filterFiles()">
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-outline-primary btn-sm" onclick="browseDropboxFiles('${folderData.path || ''}')">
                                    <i class="fas fa-sync-alt me-1"></i>Refresh
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr><th width="50%">Name</th><th width="15%">Type</th><th width="15%">Size</th><th width="20%">Modified</th></tr>
                                </thead>
                                <tbody id="fileTableBody">
                                    ${folderData.items.map(item => `
                                        <tr class="file-row" data-name="${item.name.toLowerCase()}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="${getFileIcon(item.name, item.is_folder)} me-2"></i>
                                                    ${item.is_folder ?
                `<a href="#" onclick="browseDropboxFiles('${item.path}')" class="text-decoration-none fw-bold">${item.name}</a>` :
                `<span>${item.name}</span>`}
                                                </div>
                                            </td>
                                            <td><span class="badge ${item.is_folder ? 'bg-warning' : 'bg-info'}">${item.is_folder ? 'Folder' : (item.file_type ? item.file_type.toUpperCase() : 'File')}</span></td>
                                            <td class="text-muted">${item.is_folder ? '-' : formatFileSize(item.size)}</td>
                                            <td class="text-muted">${formatDate(item.modified)}</td>
                                        </tr>
                                    `).join('')}
                                    ${folderData.items.length === 0 ? '<tr><td colspan="4" class="text-center text-muted py-4"><i class="fas fa-folder-open fa-2x mb-2"></i><br>This folder is empty</td></tr>' : ''}
                                </tbody>
                            </table>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <small class="text-muted"><i class="fas fa-info-circle me-1"></i>${folderData.items.length} items in this folder</small>
                            </div>
                            <div class="col-md-6 text-end">
                                <small class="text-muted"><i class="fab fa-dropbox me-1"></i>Synced with Dropbox</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="fas fa-times me-1"></i>Close</button>
                        <button type="button" class="btn btn-primary" onclick="showAlert('File sync features coming soon!', 'info')"><i class="fas fa-sync me-1"></i>Sync Files</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
        modal.addEventListener('hidden.bs.modal', () => document.body.removeChild(modal));
    }

    function filterFiles() {
        const searchTerm = document.getElementById('fileSearch').value.toLowerCase();
        const rows = document.querySelectorAll('#fileTableBody .file-row');
        rows.forEach(row => {
            const fileName = row.getAttribute('data-name');
            row.style.display = fileName.includes(searchTerm) ? '' : 'none';
        });
    }

    function showDropboxAuthModal(authUrl, instructions) {
        const modalHtml = `
            <div class="modal fade" id="dropboxAuthModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title"><i class="fab fa-dropbox me-2"></i>Connect Dropbox</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <strong>Instructions:</strong>
                                <ol class="mt-2 mb-0">
                                    <li>Click "Open Dropbox Authorization" below</li>
                                    <li>Log in to your Dropbox account</li>
                                    <li>Click "Allow" to grant access</li>
                                    <li>Copy the authorization code shown</li>
                                    <li>Paste it below and click "Connect"</li>
                                </ol>
                            </div>
                            <div class="text-center mb-3">
                                <a href="${authUrl}" target="_blank" class="btn btn-primary btn-lg">
                                    <i class="fab fa-dropbox me-2"></i>Open Dropbox Authorization
                                </a>
                            </div>
                            <form id="dropboxCodeForm">
                                <div class="mb-3">
                                    <label for="authCode" class="form-label">Authorization Code:</label>
                                    <textarea class="form-control" id="authCode" rows="3" placeholder="Paste the authorization code here..." required></textarea>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-success" id="connectBtn">
                                        <i class="fas fa-link me-2"></i>Connect Dropbox
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const existingModal = document.getElementById('dropboxAuthModal');
        if (existingModal) existingModal.remove();

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('dropboxAuthModal'));
        modal.show();

        document.getElementById('dropboxCodeForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const authCode = document.getElementById('authCode').value.trim();
            if (authCode) {
                await submitDropboxCode(authCode);
                modal.hide();
            }
        });
    }

    async function submitDropboxCode(authCode) {
        try {
            const token = localStorage.getItem('access_token');
            const connectBtn = document.getElementById('connectBtn');
            const originalText = connectBtn.innerHTML;

            connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Connecting...';
            connectBtn.disabled = true;

            const response = await fetch('/api/owner/dropbox/connect', {
                method: 'POST',
                headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' },
                body: JSON.stringify({ auth_code: authCode })
            });

            const result = await response.json();

            if (response.ok) {
                showAlert(`Dropbox connected successfully! Account: ${result.account_info.email}`, 'success');

                // Update UI immediately to show connected state
                const statusElement = document.getElementById('dropbox-status');
                const statusIcon = document.getElementById('dropbox-status-icon');
                const accountInfo = document.getElementById('dropbox-account-info');

                statusElement.textContent = 'Connected';
                statusElement.className = 'badge bg-success';
                statusIcon.className = 'fas fa-check-circle text-success me-2';
                accountInfo.innerHTML = `<i class="fas fa-user me-1"></i>${result.account_info.email}`;

                // Update buttons to show connected options
                updateDropboxButtons(true);

                // Refresh dashboard data
                await checkDropboxStatus();
                await loadDashboardStats();
            } else {
                showAlert(result.detail || 'Failed to connect Dropbox', 'error');
            }

            connectBtn.innerHTML = originalText;
            connectBtn.disabled = false;
        } catch (error) {
            console.error('Error submitting auth code:', error);
            showAlert('Failed to connect Dropbox', 'error');
        }
    }


    async function checkDropboxStatus() {
        console.log('Checking Dropbox status...');
        try {
            const token = localStorage.getItem('access_token');
            if (!token) {
                console.warn('No token found for Dropbox status check');
                updateDropboxButtons(false);
                return;
            }

            console.log('Fetching Dropbox status from API...');
            const response = await fetch('/api/owner/dropbox/status', {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            console.log('Dropbox status response status:', response.status);
            if (response.ok) {
                const data = await response.json();
                console.log('Dropbox status data received:', data);
                const statusElement = document.getElementById('dropbox-status');
                const statusIcon = document.getElementById('dropbox-status-icon');
                const accountInfo = document.getElementById('dropbox-account-info');

                if (data && data.email) {
                    console.log('Dropbox is connected, updating UI...');
                    statusElement.textContent = 'Connected';
                    statusElement.className = 'badge bg-success';
                    statusIcon.className = 'fas fa-check-circle text-success me-2';

                    // Update account info
                    accountInfo.innerHTML = `<i class="fas fa-user me-1"></i>${data.email}`;
                    console.log('Dropbox status updated to Connected');

                    // Show connected buttons
                    updateDropboxButtons(true);
                } else {
                    console.log('Dropbox is disconnected, updating UI...');
                    statusElement.textContent = 'Disconnected';
                    statusElement.className = 'badge bg-danger';
                    statusIcon.className = 'fas fa-exclamation-circle text-danger me-2';
                    accountInfo.innerHTML = '<i class="fas fa-times me-1"></i>Please connect to Dropbox';
                    console.log('Dropbox status updated to Disconnected');

                    // Show connect button
                    updateDropboxButtons(false);
                }
            } else {
                console.error('Failed to check Dropbox status:', response.status);
                // Set error state
                const statusElement = document.getElementById('dropbox-status');
                const statusIcon = document.getElementById('dropbox-status-icon');
                const accountInfo = document.getElementById('dropbox-account-info');

                statusElement.textContent = 'Error';
                statusElement.className = 'badge bg-warning';
                statusIcon.className = 'fas fa-exclamation-triangle text-warning me-2';
                accountInfo.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Connection check failed';

                // Show connect button on error (assume disconnected)
                updateDropboxButtons(false);
            }
        } catch (error) {
            console.error('Error checking Dropbox status:', error);
            // Set error state
            const statusElement = document.getElementById('dropbox-status');
            const statusIcon = document.getElementById('dropbox-status-icon');
            const accountInfo = document.getElementById('dropbox-account-info');

            statusElement.textContent = 'Error';
            statusElement.className = 'badge bg-warning';
            statusIcon.className = 'fas fa-exclamation-triangle text-warning me-2';
            accountInfo.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Unable to check status';

            // Show connect button on error (assume disconnected)
            updateDropboxButtons(false);
        }
    }

    function updateDropboxButtons(isConnected) {
        const loadingButtons = document.getElementById('dropbox-loading-buttons');
        const connectedButtons = document.getElementById('dropbox-connected-buttons');
        const notConnectedButtons = document.getElementById('dropbox-not-connected-buttons');

        // Hide loading state
        if (loadingButtons) loadingButtons.classList.add('d-none');

        if (isConnected) {
            // Show connected buttons, hide connect button
            if (connectedButtons) connectedButtons.classList.remove('d-none');
            if (notConnectedButtons) notConnectedButtons.classList.add('d-none');
        } else {
            // Show connect button, hide connected buttons
            if (connectedButtons) connectedButtons.classList.add('d-none');
            if (notConnectedButtons) notConnectedButtons.classList.remove('d-none');
        }
    }

    // CA_Firm Files Management
    let selectedFiles = [];
    let allCAFirmFiles = [];
    let filteredFiles = []; // For search results

    // Pagination settings
    const ITEMS_PER_PAGE = 10;
    let currentPage = 1;
    let totalPages = 1;

    // Search state
    let currentSearchTerm = '';
    let currentFileTypeFilter = '';

    async function loadCAFirmFiles() {
        try {
            const token = localStorage.getItem('access_token');
            if (!token) {
                showAlert('Please login first', 'error');
                return;
            }

            // Show loading state
            const container = document.getElementById('ca-firm-files-container');
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="text-muted mt-2">Refreshing files from CA_Firm folder...</p>
                </div>
            `;

            // Add cache-busting parameter and timestamp
            const timestamp = new Date().getTime();
            const response = await fetch(`/api/owner/dropbox/ca-firm-files?refresh=${timestamp}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            if (response.ok) {
                const data = await response.json();
                allCAFirmFiles = data.files;

                // Reset pagination and search state
                currentPage = 1;
                filteredFiles = [];
                currentSearchTerm = '';
                currentFileTypeFilter = '';

                // Clear search inputs
                document.getElementById('search-input').value = '';
                document.getElementById('file-type-filter').value = '';

                displayCAFirmFiles(getFilesToDisplay());
                document.getElementById('file-actions').classList.remove('d-none');

                // Update search results info
                updateSearchResultsInfo();

                // Show success message with file count
                showAlert(`Refreshed! Found ${data.total_files} files in CA_Firm folder`, 'success');

                // Update last sync time
                updateLastSyncTime();
            } else {
                const errorText = await response.text();
                console.error('CA_Firm files error:', response.status, errorText);

                const container = document.getElementById('ca-firm-files-container');
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <p class="text-muted">
                            ${response.status === 404 ? 'CA_Firm folder not found in your Dropbox' :
                        response.status === 401 ? 'Please reconnect to Dropbox - authentication expired' :
                            'Error loading files from CA_Firm folder'}
                        </p>
                        <p class="text-muted small">Status: ${response.status}</p>
                        <div class="mt-3">
                            <button class="btn btn-primary me-2" onclick="checkDropboxStatus()">
                                <i class="fab fa-dropbox me-1"></i>Check Dropbox Connection
                            </button>
                            <button class="btn btn-outline-primary" onclick="loadCAFirmFiles()">
                                <i class="fas fa-sync-alt me-1"></i>Retry
                            </button>
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading CA_Firm files:', error);
            showAlert('Failed to refresh files. Please try again.', 'error');
            const container = document.getElementById('ca-firm-files-container');
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                    <p class="text-muted">Failed to load files from CA_Firm folder</p>
                    <p class="text-muted small">Error: ${error.message}</p>
                    <button class="btn btn-outline-primary" onclick="loadCAFirmFiles()">
                        <i class="fas fa-sync-alt me-1"></i>Retry
                    </button>
                </div>
            `;
        }
    }

    function displayCAFirmFiles(files) {
        const container = document.getElementById('ca-firm-files-container');

        if (files.length === 0) {
            const isSearchActive = currentSearchTerm || currentFileTypeFilter;
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-${isSearchActive ? 'search' : 'folder-open'} fa-2x text-muted mb-2"></i>
                    <p class="text-muted">
                        ${isSearchActive ? 'No files found matching your search' : 'No files found in CA_Firm folder'}
                    </p>
                    <p class="text-muted small">
                        ${isSearchActive ? 'Try adjusting your search terms or filters' : 'Make sure you have documents in your /CA_Firm folder on Dropbox'}
                    </p>
                    ${isSearchActive ? `
                        <button class="btn btn-outline-primary btn-sm mt-2" onclick="clearSearch()">
                            <i class="fas fa-times me-1"></i>Clear Search
                        </button>
                    ` : ''}
                </div>
            `;
            return;
        }

        // Calculate pagination
        totalPages = Math.ceil(files.length / ITEMS_PER_PAGE);
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        const paginatedFiles = files.slice(startIndex, endIndex);

        const filesHTML = paginatedFiles.map((file, index) => {
            const fileIcon = getFileIcon(file.file_extension);
            const fileDate = file.modified ? new Date(file.modified).toLocaleDateString() : 'Unknown';
            const isEven = index % 2 === 0;

            // Highlight search terms in file name
            let displayName = file.name;
            if (currentSearchTerm) {
                const regex = new RegExp(`(${currentSearchTerm})`, 'gi');
                displayName = file.name.replace(regex, '<mark class="bg-warning">$1</mark>');
            }

            return `
                <div class="row align-items-center py-3 file-row ${isEven ? 'bg-light bg-opacity-25' : ''}" 
                     style="transition: all 0.2s ease; cursor: pointer;"
                     onmouseover="this.style.backgroundColor='#f8f9fa'" 
                     onmouseout="this.style.backgroundColor='${isEven ? 'rgba(248, 249, 250, 0.25)' : 'transparent'}'">
                    <div class="col-md-1 px-3">
                        <div class="form-check" style="margin-left: 8px;">
                            <input class="form-check-input" type="checkbox" value="${file.path}" 
                                   id="file-${file.dropbox_file_id}"
                                   onchange="toggleFileSelection('${file.path}')"
                                   style="transform: scale(1.1);">
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="d-flex align-items-center">
                            <div class="file-icon-container me-3" style="width: 40px; text-align: center;">
                                <i class="${fileIcon} fa-2x"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-semibold text-dark mb-1" style="font-size: 0.95rem;">${displayName}</div>
                                <small class="text-muted d-block" style="font-size: 0.8rem;">
                                    <i class="fas fa-folder me-1"></i>${file.path}
                                </small>
                                <small class="text-muted" style="font-size: 0.75rem;">
                                    <i class="fas fa-calendar me-1"></i>Modified: ${fileDate}
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <span class="badge ${getFileTypeBadge(file.file_extension)} px-3 py-2" style="font-size: 0.8rem;">
                            ${file.file_extension.toUpperCase()}
                        </span>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="text-muted fw-medium">${file.size_formatted}</div>
                        <small class="text-muted" style="font-size: 0.75rem;">${file.size} bytes</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="btn-group btn-group-sm" role="group">
                            <button class="btn btn-outline-primary btn-sm shadow-sm" 
                                    onclick="viewFileContent('${file.path}', '${file.name}', '${file.file_extension}')" 
                                    title="View Content"
                                    style="border-radius: 6px 0 0 6px;">
                                <i class="fas fa-eye me-1"></i>View
                            </button>
                            <button class="btn btn-outline-success btn-sm shadow-sm" 
                                    onclick="downloadFile('${file.path}', '${file.name}')" 
                                    title="Download File"
                                    style="border-radius: 0 6px 6px 0;">
                                <i class="fas fa-download me-1"></i>Download
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = `
            <div class="border rounded shadow-sm">
                <div class="row align-items-center py-3 border-bottom bg-gradient" 
                     style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <div class="col-md-1 px-3">
                        <div class="form-check" style="margin-left: 8px;">
                            <input class="form-check-input bg-white" type="checkbox" id="select-all-files" 
                                   onchange="toggleAllFiles()" style="transform: scale(1.2);">
                        </div>
                    </div>
                    <div class="col-md-5">
                        <i class="fas fa-file-alt me-2"></i>
                        <strong>File Name & Details</strong>
                    </div>
                    <div class="col-md-2 text-center">
                        <i class="fas fa-tag me-1"></i>
                        <strong>Type</strong>
                    </div>
                    <div class="col-md-2 text-center">
                        <i class="fas fa-weight-hanging me-1"></i>
                        <strong>Size</strong>
                    </div>
                    <div class="col-md-2 text-center">
                        <i class="fas fa-cogs me-1"></i>
                        <strong>Actions</strong>
                    </div>
                </div>
                ${filesHTML}
            </div>
            ${generatePaginationControls(files.length)}
            <div class="mt-4 p-3 bg-light rounded">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <p class="mb-0 text-muted">
                            <i class="fas fa-info-circle text-primary me-2"></i>
                            <strong>${files.length} files</strong> found in CA_Firm folder. 
                            Select files to start the AI embedding process.
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <small class="text-muted">
                            <i class="fas fa-brain me-1"></i>
                            Ready for AI processing
                        </small>
                    </div>
                </div>
            </div>
        `;
    }

    function getFileIcon(extension) {
        const iconMap = {
            'pdf': 'fas fa-file-pdf text-danger',
            'doc': 'fas fa-file-word text-primary',
            'docx': 'fas fa-file-word text-primary',
            'xls': 'fas fa-file-excel text-success',
            'xlsx': 'fas fa-file-excel text-success',
            'csv': 'fas fa-file-csv text-info',
            'ppt': 'fas fa-file-powerpoint text-warning',
            'pptx': 'fas fa-file-powerpoint text-warning',
            'txt': 'fas fa-file-alt text-secondary',
            'default': 'fas fa-file text-muted'
        };
        return iconMap[extension.toLowerCase()] || iconMap['default'];
    }

    function getFileTypeBadge(extension) {
        const badgeMap = {
            'pdf': 'bg-danger text-white',
            'doc': 'bg-primary text-white',
            'docx': 'bg-primary text-white',
            'xls': 'bg-success text-white',
            'xlsx': 'bg-success text-white',
            'csv': 'bg-info text-white',
            'ppt': 'bg-warning text-dark',
            'pptx': 'bg-warning text-dark',
            'txt': 'bg-secondary text-white',
            'default': 'bg-light text-dark'
        };
        return badgeMap[extension.toLowerCase()] || badgeMap['default'];
    }

    function generatePaginationControls(totalFiles) {
        if (totalFiles <= ITEMS_PER_PAGE) {
            return '';
        }

        const startItem = (currentPage - 1) * ITEMS_PER_PAGE + 1;
        const endItem = Math.min(currentPage * ITEMS_PER_PAGE, totalFiles);

        let paginationHTML = `
            <div class="d-flex justify-content-between align-items-center mt-3 px-3 py-2">
                <div class="text-muted small">
                    Showing ${startItem}-${endItem} of ${totalFiles} files
                </div>
                <div class="d-flex align-items-center">
        `;

        // Previous button
        if (currentPage > 1) {
            paginationHTML += `
                <button class="btn btn-outline-secondary btn-sm me-2" onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i> Previous
                </button>
            `;
        }

        // Page numbers
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        if (startPage > 1) {
            paginationHTML += `
                <button class="btn btn-outline-secondary btn-sm me-1" onclick="changePage(1)">1</button>
            `;
            if (startPage > 2) {
                paginationHTML += `<span class="me-2">...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === currentPage;
            paginationHTML += `
                <button class="btn ${isActive ? 'btn-primary' : 'btn-outline-secondary'} btn-sm me-1" 
                        onclick="changePage(${i})" ${isActive ? 'disabled' : ''}>
                    ${i}
                </button>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span class="me-2">...</span>`;
            }
            paginationHTML += `
                <button class="btn btn-outline-secondary btn-sm me-1" onclick="changePage(${totalPages})">${totalPages}</button>
            `;
        }

        // Next button
        if (currentPage < totalPages) {
            paginationHTML += `
                <button class="btn btn-outline-secondary btn-sm ms-2" onclick="changePage(${currentPage + 1})">
                    Next <i class="fas fa-chevron-right"></i>
                </button>
            `;
        }

        paginationHTML += `
                </div>
            </div>
        `;

        return paginationHTML;
    }

    function changePage(page) {
        if (page >= 1 && page <= totalPages && page !== currentPage) {
            currentPage = page;
            displayCAFirmFiles(getFilesToDisplay());
        }
    }

    // Search and Filter Functions
    function performSearch() {
        const searchInput = document.getElementById('search-input');
        const fileTypeFilter = document.getElementById('file-type-filter');

        currentSearchTerm = searchInput.value.toLowerCase().trim();
        currentFileTypeFilter = fileTypeFilter.value.toLowerCase();

        // Reset to first page when searching
        currentPage = 1;

        // Filter files based on search criteria
        filteredFiles = allCAFirmFiles.filter(file => {
            // Text search in file name
            const nameMatch = currentSearchTerm === '' ||
                file.name.toLowerCase().includes(currentSearchTerm);

            // File type filter
            const typeMatch = currentFileTypeFilter === '' ||
                file.file_extension.toLowerCase() === currentFileTypeFilter;

            return nameMatch && typeMatch;
        });

        // Update display
        displayCAFirmFiles(getFilesToDisplay());
        updateSearchResultsInfo();
    }

    function clearSearch() {
        // Clear search inputs
        document.getElementById('search-input').value = '';
        document.getElementById('file-type-filter').value = '';

        // Reset search state
        currentSearchTerm = '';
        currentFileTypeFilter = '';
        currentPage = 1;
        filteredFiles = [];

        // Display all files
        displayCAFirmFiles(getFilesToDisplay());
        updateSearchResultsInfo();
    }

    function getFilesToDisplay() {
        // Return filtered files if search is active, otherwise all files
        return (currentSearchTerm || currentFileTypeFilter) ? filteredFiles : allCAFirmFiles;
    }

    function updateSearchResultsInfo() {
        const resultsInfo = document.getElementById('search-results-info');
        // Element was removed as per user request, so just return
        if (!resultsInfo) {
            return;
        }

        const filesToDisplay = getFilesToDisplay();

        if (currentSearchTerm || currentFileTypeFilter) {
            const searchText = currentSearchTerm ? `"${currentSearchTerm}"` : '';
            const typeText = currentFileTypeFilter ? `${currentFileTypeFilter.toUpperCase()} files` : '';
            const searchQuery = [searchText, typeText].filter(Boolean).join(' in ');

            resultsInfo.innerHTML = `
                <i class="fas fa-search me-1"></i>
                Found ${filesToDisplay.length} file${filesToDisplay.length !== 1 ? 's' : ''}
                ${searchQuery ? `for ${searchQuery}` : ''}
            `;
        } else {
            resultsInfo.innerHTML = `
                <i class="fas fa-files me-1"></i>
                ${allCAFirmFiles.length} total file${allCAFirmFiles.length !== 1 ? 's' : ''}
            `;
        }
    }

    function toggleFileSelection(filePath) {
        const checkbox = document.querySelector(`input[value="${filePath}"]`);
        if (checkbox.checked) {
            if (!selectedFiles.includes(filePath)) {
                selectedFiles.push(filePath);
            }
        } else {
            selectedFiles = selectedFiles.filter(path => path !== filePath);
        }
        updateEmbeddingButton();
    }

    function toggleAllFiles() {
        const selectAllCheckbox = document.getElementById('select-all-files');
        const fileCheckboxes = document.querySelectorAll('.file-row input[type="checkbox"]');

        fileCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
            toggleFileSelection(checkbox.value);
        });
    }

    function selectAllFiles() {
        const fileCheckboxes = document.querySelectorAll('.file-row input[type="checkbox"]');
        const selectAllCheckbox = document.getElementById('select-all-files');

        fileCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
            if (!selectedFiles.includes(checkbox.value)) {
                selectedFiles.push(checkbox.value);
            }
        });

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = true;
        }

        updateEmbeddingButton();
    }

    function deselectAllFiles() {
        const fileCheckboxes = document.querySelectorAll('.file-row input[type="checkbox"]');
        const selectAllCheckbox = document.getElementById('select-all-files');

        fileCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
        }

        selectedFiles = [];
        updateEmbeddingButton();
    }

    function updateEmbeddingButton() {
        const embeddingBtn = document.getElementById('embedding-btn');
        if (embeddingBtn) {
            embeddingBtn.disabled = selectedFiles.length === 0;
            embeddingBtn.innerHTML = selectedFiles.length > 0
                ? `<i class="fas fa-brain me-1"></i>Start Embedding (${selectedFiles.length} files)`
                : `<i class="fas fa-brain me-1"></i>Start Embedding Process`;
        }
    }

    function startEmbeddingProcess() {
        if (selectedFiles.length === 0) {
            showAlert('Please select at least one file to process', 'warning');
            return;
        }

        // Show confirmation dialog
        if (confirm(`Start AI embedding process for ${selectedFiles.length} selected files?`)) {
            showAlert(`Starting embedding process for ${selectedFiles.length} files...`, 'info');

            // TODO: Implement actual embedding process
            console.log('Starting embedding for files:', selectedFiles);

            // Simulate processing
            simulateEmbeddingProcess();
        }
    }

    function simulateEmbeddingProcess() {
        const processingSection = document.getElementById('processing-status-section');
        processingSection.classList.remove('d-none');

        // Update processing status
        document.getElementById('current-job').textContent = `Processing ${selectedFiles.length} files for AI embedding`;
        document.getElementById('files-processed').textContent = '0';
        document.getElementById('total-files').textContent = selectedFiles.length.toString();
        document.getElementById('estimated-time').textContent = `${Math.ceil(selectedFiles.length * 0.5)} minutes`;

        // Simulate progress
        let processed = 0;
        const interval = setInterval(() => {
            processed++;
            const progress = (processed / selectedFiles.length) * 100;

            document.getElementById('processing-progress').style.width = `${progress}%`;
            document.getElementById('files-processed').textContent = processed.toString();

            if (processed >= selectedFiles.length) {
                clearInterval(interval);
                showAlert('Embedding process completed successfully!', 'success');
                processingSection.classList.add('d-none');
            }
        }, 1000);
    }

    async function viewFileContent(filePath, fileName, fileExtension) {
        try {
            const token = localStorage.getItem('access_token');
            if (!token) {
                showAlert('Please login first', 'error');
                return;
            }

            showAlert(`Loading ${fileName}...`, 'info');

            // For PDF files, download and show in blob URL
            if (fileExtension.toLowerCase() === 'pdf') {
                try {
                    const downloadUrl = `/api/owner/dropbox/download-file?file_path=${encodeURIComponent(filePath)}`;
                    const response = await fetch(downloadUrl, {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });

                    if (response.ok) {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);

                        // Open PDF in new tab
                        const newWindow = window.open(url, '_blank');
                        if (newWindow) {
                            newWindow.document.title = fileName;
                            showAlert(`Opening ${fileName} in new tab`, 'success');

                            // Clean up the blob URL after a delay
                            setTimeout(() => {
                                window.URL.revokeObjectURL(url);
                            }, 10000);
                        } else {
                            showAlert('Please allow popups to view PDF files', 'warning');
                        }
                    } else {
                        const error = await response.json();
                        showAlert(error.detail || 'Failed to load PDF file', 'error');
                    }
                } catch (error) {
                    console.error('Error loading PDF:', error);
                    showAlert('Failed to load PDF file', 'error');
                }
            } else {
                // For other file types (XLS, XLSX, CSV, DOC, DOCX, TXT), show file info modal
                const response = await fetch(`/api/owner/dropbox/view-file?file_path=${encodeURIComponent(filePath)}`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const fileInfo = await response.json();
                    showFileInfoModal(fileInfo);
                } else {
                    const error = await response.json();
                    showAlert(error.detail || 'Failed to load file information', 'error');
                }
            }
        } catch (error) {
            console.error('Error viewing file:', error);
            showAlert('Failed to view file content', 'error');
        }
    }

    function showFileInfoModal(fileInfo) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="${getFileIcon(fileInfo.file_extension)} me-2"></i>
                            ${fileInfo.filename}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>File Path:</strong><br><code>${fileInfo.file_path}</code></p>
                                <p><strong>File Type:</strong> <span class="badge ${getFileTypeBadge(fileInfo.file_extension)}">${fileInfo.file_extension.toUpperCase()}</span></p>
                                <p><strong>File Size:</strong> ${fileInfo.size_formatted}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Last Modified:</strong><br>${fileInfo.modified ? new Date(fileInfo.modified).toLocaleString() : 'Unknown'}</p>
                                <p><strong>Preview Available:</strong> ${fileInfo.can_preview ? '✅ Yes' : '❌ No'}</p>
                                <p><strong>Can Download:</strong> ${fileInfo.can_download ? '✅ Yes' : '❌ No'}</p>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            ${fileInfo.message}
                        </div>
                    </div>
                    <div class="modal-footer">
                        ${fileInfo.can_preview ? `<button type="button" class="btn btn-primary" onclick="window.open('${fileInfo.preview_url}', '_blank')">
                            <i class="fas fa-eye me-1"></i>Preview
                        </button>` : ''}
                        <button type="button" class="btn btn-success" onclick="downloadFileFromModal('${fileInfo.download_url}', '${fileInfo.filename}')">
                            <i class="fas fa-download me-1"></i>Download
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Remove modal from DOM when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    async function downloadFile(filePath, fileName) {
        try {
            const token = localStorage.getItem('access_token');
            if (!token) {
                showAlert('Please login first', 'error');
                return;
            }

            showAlert(`Preparing download for ${fileName}...`, 'info');

            // Create download URL with token
            const downloadUrl = `/api/owner/dropbox/download-file?file_path=${encodeURIComponent(filePath)}`;

            // Create a temporary form to handle the download with authorization
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = downloadUrl;
            form.style.display = 'none';

            // Add authorization header via hidden input (for simple cases)
            // For more complex auth, we'll use fetch and blob

            try {
                const response = await fetch(downloadUrl, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showAlert(`${fileName} downloaded successfully!`, 'success');
                } else {
                    const error = await response.json();
                    showAlert(error.detail || 'Failed to download file', 'error');
                }
            } catch (error) {
                console.error('Download error:', error);
                showAlert('Failed to download file', 'error');
            }
        } catch (error) {
            console.error('Error downloading file:', error);
            showAlert('Failed to download file', 'error');
        }
    }

    function downloadFileFromModal(downloadUrl, fileName) {
        // Close modal first
        const modal = document.querySelector('.modal.show');
        if (modal) {
            const bootstrapModal = bootstrap.Modal.getInstance(modal);
            bootstrapModal.hide();
        }

        // Extract file path from URL and call downloadFile
        const urlParams = new URLSearchParams(downloadUrl.split('?')[1]);
        const filePath = urlParams.get('file_path');
        downloadFile(filePath, fileName);
    }

    // Update last sync time
    function updateLastSyncTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
        document.getElementById('last-sync-time').textContent = timeString;
    }

    // Refresh all data
    async function refreshAllData() {
        showAlert('Refreshing data...', 'info');

        try {
            // Load dashboard stats first
            console.log('Refreshing: Loading dashboard stats...');
            await loadDashboardStats();

            // Then load other data
            await Promise.all([
                checkDropboxStatus(),
                loadCAFirmFiles()
            ]);

            updateLastSyncTime();
            showAlert('Data refreshed successfully!', 'success');
        } catch (error) {
            console.error('Error refreshing data:', error);
            showAlert('Some data could not be refreshed', 'warning');
        }
    }

    // Force update status if it's still loading after 3 seconds
    function forceStatusUpdate() {
        const statusElement = document.getElementById('dropbox-status');
        const accountInfo = document.getElementById('dropbox-account-info');

        if (statusElement && statusElement.textContent.includes('Checking') &&
            accountInfo && accountInfo.textContent.includes('Status loading')) {
            console.log('Forcing status update due to loading timeout...');
            checkDropboxStatus();
        }
    }

    // Force load dashboard stats
    function forceLoadStats() {
        console.log('Force loading dashboard stats...');
        loadDashboardStats();
    }

    // Simple test function for manual testing
    function testHeaderUpdate() {
        console.log('Testing header update manually...');
        const welcomeMessage = document.getElementById('welcome-message');
        const firmInfo = document.getElementById('firm-info');

        if (welcomeMessage) {
            welcomeMessage.textContent = 'Welcome back, Manish Mundra!';
            console.log('Updated welcome message');
        }

        if (firmInfo) {
            firmInfo.innerHTML = '<i class="fas fa-building me-1"></i>CA Professional Services';
            console.log('Updated firm info');
        }

        return 'Header updated manually';
    }

    // Make functions globally accessible for testing
    window.testHeaderUpdate = testHeaderUpdate;
    window.forceLoadStats = forceLoadStats;
    window.loadDashboardStats = loadDashboardStats;

    // Initialize dashboard when page loads
    document.addEventListener('DOMContentLoaded', function () {
        console.log('Dashboard initializing...');
        updateLastSyncTime();

        // Check if we have a token first
        const token = localStorage.getItem('token');
        if (token) {
            console.log('Token found, loading dashboard stats immediately...');
            loadDashboardStats();

            // Also try after a short delay as backup
            setTimeout(function () {
                console.log('Loading dashboard stats in 500ms (backup)...');
                loadDashboardStats();
            }, 500);
        } else {
            console.log('No token found, skipping dashboard stats loading');
        }

        setTimeout(function () {
            console.log('Loading Dropbox status in 1000ms...');
            checkDropboxStatus();
        }, 1000);

        setTimeout(function () {
            console.log('Loading CA Firm files in 1500ms...');
            loadCAFirmFiles();
        }, 1500);

        // Force update if still loading after 3 seconds
        setTimeout(forceStatusUpdate, 3000);

        // Force load stats again after 5 seconds if still showing "Loading..."
        setTimeout(function () {
            const firmInfo = document.getElementById('firm-info');
            if (firmInfo && firmInfo.textContent === 'Loading...') {
                console.log('Firm info still loading, forcing stats reload...');
                forceLoadStats();
            }
        }, 5000);

        // Update sync time every minute
        setInterval(updateLastSyncTime, 60000);

        // Update dashboard stats every 30 seconds
        setInterval(function () {
            loadDashboardStats();
            checkDropboxStatus();
        }, 30000);
    });

    // Also try loading when window is fully loaded
    window.addEventListener('load', function () {
        console.log('Window fully loaded, checking for token and loading stats...');
        const token = localStorage.getItem('token');
        if (token) {
            console.log('Token found in window load, loading stats...');
            setTimeout(forceLoadStats, 1000);
        }
    });

    // Listen for storage changes (when token is set)
    window.addEventListener('storage', function (e) {
        if (e.key === 'token' && e.newValue) {
            console.log('Token detected in storage, loading dashboard stats...');
            setTimeout(loadDashboardStats, 100);
        }
    });

    // Custom event for when authentication completes
    document.addEventListener('authComplete', function () {
        console.log('Authentication complete event received, loading stats...');
        loadDashboardStats();
    });

    // ============================================================================
    // CLIENT MANAGEMENT FUNCTIONALITY
    // ============================================================================

    let currentClients = [];
    let currentClientFolders = [];
    let selectedClientId = null;
    let currentOwnerId = null;

    function loadClientManagementModal() {
        // Create and show the client management modal
        const modalHTML = `
            <div class="modal fade" id="clientManagementModal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-users me-2"></i>Client Management
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body p-0">
                            <!-- Navigation Tabs -->
                            <ul class="nav nav-tabs" id="clientTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="clients-tab" data-bs-toggle="tab" 
                                            data-bs-target="#clients-panel" type="button" role="tab">
                                        <i class="fas fa-list me-1"></i>All Clients
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="add-client-tab" data-bs-toggle="tab" 
                                            data-bs-target="#add-client-panel" type="button" role="tab">
                                        <i class="fas fa-plus me-1"></i>Add Client
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="client-details-tab" data-bs-toggle="tab" 
                                            data-bs-target="#client-details-panel" type="button" role="tab" disabled>
                                        <i class="fas fa-user me-1"></i>Client Details
                                    </button>
                                </li>
                            </ul>

                            <!-- Tab Content -->
                            <div class="tab-content" id="clientTabContent">
                                <!-- All Clients Panel -->
                                <div class="tab-pane fade show active" id="clients-panel">
                                    <div class="p-4">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">Client List</h6>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-outline-primary btn-sm" onclick="refreshClientList()">
                                                    <i class="fas fa-sync-alt me-1"></i>Refresh
                                                </button>
                                                <button class="btn btn-success btn-sm" onclick="switchToAddClientTab()">
                                                    <i class="fas fa-plus me-1"></i>Add New Client
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <!-- Search and Filter -->
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" id="clientSearchInput" 
                                                       placeholder="Search clients..." onkeyup="filterClients()">
                                            </div>
                                            <div class="col-md-3">
                                                <select class="form-select" id="clientTypeFilter" onchange="filterClients()">
                                                    <option value="">All Types</option>
                                                    <option value="individual">Individual</option>
                                                    <option value="business">Business</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <select class="form-select" id="clientStatusFilter" onchange="filterClients()">
                                                    <option value="">All Status</option>
                                                    <option value="active">Active</option>
                                                    <option value="inactive">Inactive</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Clients Table -->
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Email</th>
                                                        <th>Type</th>
                                                        <th>Status</th>
                                                        <th>Access</th>
                                                        <th>Created</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="clientsTableBody">
                                                    <tr>
                                                        <td colspan="7" class="text-center py-4">
                                                            <div class="spinner-border text-primary" role="status">
                                                                <span class="visually-hidden">Loading...</span>
                                                            </div>
                                                            <div class="mt-2">Loading clients...</div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add Client Panel -->
                                <div class="tab-pane fade" id="add-client-panel">
                                    <div class="p-4">
                                        <h6 class="mb-3">Add New Client</h6>
                                        <form id="addClientForm" onsubmit="submitNewClient(event)">
                                            <div class="row">
                                                <!-- Basic Information -->
                                                <div class="col-md-6">
                                                    <h6 class="text-muted mb-3">Basic Information</h6>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label">Full Name *</label>
                                                        <input type="text" class="form-control" name="full_name" required>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label">Email Address *</label>
                                                        <input type="email" class="form-control" name="email" required>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label">Client Type</label>
                                                        <select class="form-select" name="client_type" onchange="toggleCompanyField(this)">
                                                            <option value="individual">Individual</option>
                                                            <option value="business">Business</option>
                                                        </select>
                                                    </div>
                                                    
                                                    <div class="mb-3" id="companyNameGroup" style="display: none;">
                                                        <label class="form-label">Company Name</label>
                                                        <input type="text" class="form-control" name="company_name">
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label">Phone</label>
                                                        <input type="tel" class="form-control" name="phone">
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label">Password *</label>
                                                        <input type="password" class="form-control" name="password" 
                                                               minlength="8" required>
                                                        <div class="form-text">Minimum 8 characters</div>
                                                    </div>
                                                </div>
                                                
                                                <!-- Access & Settings -->
                                                <div class="col-md-6">
                                                    <h6 class="text-muted mb-3">Access & Settings</h6>
                                                    
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" 
                                                                   name="has_web_access" checked>
                                                            <label class="form-check-label">Web Panel Access</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" 
                                                                   name="has_api_access">
                                                            <label class="form-check-label">API Access</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" 
                                                                   name="bot_access_enabled" checked>
                                                            <label class="form-check-label">AI Bot Access</label>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label">Daily Question Limit</label>
                                                        <input type="number" class="form-control" name="daily_question_limit" 
                                                               value="50" min="1" max="1000">
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label">Folder Access</label>
                                                        <div class="form-text mb-2">Select folders this client can access</div>
                                                        <div id="folderAccessTree" class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                                            <div class="text-center py-2">
                                                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                                                        onclick="loadFolderTree()">
                                                                    <i class="fas fa-folder me-1"></i>Load Folders
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex justify-content-end gap-2 mt-4">
                                                <button type="button" class="btn btn-outline-secondary" onclick="resetAddClientForm()">
                                                    <i class="fas fa-undo me-1"></i>Reset
                                                </button>
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-plus me-1"></i>Add Client
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <!-- Client Details Panel -->
                                <div class="tab-pane fade" id="client-details-panel">
                                    <div class="p-4">
                                        <div id="clientDetailsContent">
                                            <div class="text-center py-4 text-muted">
                                                Select a client to view details
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if present
        const existingModal = document.getElementById('clientManagementModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Show modal and load clients
        const modal = new bootstrap.Modal(document.getElementById('clientManagementModal'));
        modal.show();

        // Load client list when modal opens
        loadClientList();
    }

    function loadClientList() {
        fetch('/api/owner/clients/', {
            headers: {
                'Authorization': 'Bearer ' + localStorage.getItem('access_token')
            }
        })
            .then(response => {
                if (!response.ok) throw new Error('Failed to load clients');
                return response.json();
            })
            .then(clients => {
                currentClients = clients;
                displayClientList(clients);
            })
            .catch(error => {
                console.error('Error loading clients:', error);
                document.getElementById('clientsTableBody').innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error loading clients: ${error.message}
                    </td>
                </tr>
            `;
            });
    }

    function displayClientList(clients) {
        const tbody = document.getElementById('clientsTableBody');

        if (clients.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-muted">
                        <i class="fas fa-users me-2"></i>
                        No clients found. Add your first client using the "Add Client" tab.
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = clients.map(client => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-2 me-2">
                            <i class="fas fa-${client.client_type === 'business' ? 'building' : 'user'} text-primary"></i>
                        </div>
                        <div>
                            <div class="fw-semibold">${client.full_name}</div>
                            ${client.company_name ? `<small class="text-muted">${client.company_name}</small>` : ''}
                        </div>
                    </div>
                </td>
                <td>${client.email}</td>
                <td>
                    <span class="badge bg-${client.client_type === 'business' ? 'info' : 'secondary'}">
                        ${client.client_type}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${client.is_active ? 'success' : 'danger'}">
                        ${client.is_active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>
                    <div class="d-flex gap-1">
                        ${client.has_web_access ? '<span class="badge bg-primary">Web</span>' : ''}
                        ${client.has_api_access ? '<span class="badge bg-info">API</span>' : ''}
                        ${client.bot_access_enabled ? '<span class="badge bg-success">Bot</span>' : ''}
                    </div>
                </td>
                <td>
                    <small class="text-muted">
                        ${new Date(client.created_at).toLocaleDateString()}
                    </small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewClientDetails(${client.id})" 
                                title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editClient(${client.id})" 
                                title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-${client.is_active ? 'secondary' : 'success'}" 
                                onclick="toggleClientStatus(${client.id})" 
                                title="${client.is_active ? 'Deactivate' : 'Activate'}">
                            <i class="fas fa-${client.is_active ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteClient(${client.id})" 
                                title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    function filterClients() {
        const searchTerm = document.getElementById('clientSearchInput').value.toLowerCase();
        const typeFilter = document.getElementById('clientTypeFilter').value;
        const statusFilter = document.getElementById('clientStatusFilter').value;

        const filteredClients = currentClients.filter(client => {
            const matchesSearch = !searchTerm ||
                client.full_name.toLowerCase().includes(searchTerm) ||
                client.email.toLowerCase().includes(searchTerm) ||
                (client.company_name && client.company_name.toLowerCase().includes(searchTerm));

            const matchesType = !typeFilter || client.client_type === typeFilter;
            const matchesStatus = !statusFilter ||
                (statusFilter === 'active' && client.is_active) ||
                (statusFilter === 'inactive' && !client.is_active);

            return matchesSearch && matchesType && matchesStatus;
        });

        displayClientList(filteredClients);
    }

    function refreshClientList() {
        loadClientList();
        showAlert('Client list refreshed', 'success');
    }

    function switchToAddClientTab() {
        const addClientTab = new bootstrap.Tab(document.getElementById('add-client-tab'));
        addClientTab.show();
    }

    function toggleCompanyField(select) {
        const companyGroup = document.getElementById('companyNameGroup');
        if (select.value === 'business') {
            companyGroup.style.display = 'block';
            companyGroup.querySelector('input').required = true;
        } else {
            companyGroup.style.display = 'none';
            companyGroup.querySelector('input').required = false;
        }
    }

    function loadFolderTree() {
        const treeContainer = document.getElementById('folderAccessTree');

        if (!currentOwnerId) {
            treeContainer.innerHTML = `
                <div class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Owner information not loaded yet. Please wait...
                </div>
            `;
            return;
        }

        treeContainer.innerHTML = '<div class="text-center py-2"><i class="fas fa-spinner fa-spin"></i> Loading folders...</div>';

        fetch(`/api/owner/clients/folder-tree/${currentOwnerId}`, {
            headers: {
                'Authorization': 'Bearer ' + localStorage.getItem('access_token')
            }
        })
            .then(response => {
                if (!response.ok) throw new Error('Failed to load folders');
                return response.json();
            })
            .then(folderTree => {
                currentClientFolders = folderTree;
                displayFolderTree(folderTree, treeContainer);
            })
            .catch(error => {
                console.error('Error loading folder tree:', error);
                treeContainer.innerHTML = `
                    <div class="text-danger mb-3">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Error loading folders: ${error.message}
                    </div>
                    <div class="text-muted mb-2">
                        <small>You can manually enter folder paths below:</small>
                    </div>
                    <div class="input-group">
                        <input type="text" class="form-control" id="manualFolderPath" 
                               placeholder="Enter folder path (e.g., /documents/tax)"
                               onkeypress="if(event.key==='Enter') addManualFolder()">
                        <button class="btn btn-outline-primary" type="button" onclick="addManualFolder()">
                            <i class="fas fa-plus"></i> Add
                        </button>
                    </div>
                    <div id="manualFolderList" class="mt-2"></div>
                `;
            });
    }

    function displayFolderTree(node, container, level = 0) {
        const indent = level * 20;
        const hasChildren = node.children && node.children.length > 0;

        const folderHTML = `
            <div class="folder-item" style="margin-left: ${indent}px;">
                <div class="form-check">
                    <input class="form-check-input folder-checkbox" type="checkbox" 
                           value="${node.path}" id="folder_${node.path.replace(/[^a-zA-Z0-9]/g, '_')}"
                           onchange="handleFolderSelection(this)">
                    <label class="form-check-label d-flex align-items-center" 
                           for="folder_${node.path.replace(/[^a-zA-Z0-9]/g, '_')}">
                        <i class="fas fa-folder text-warning me-1"></i>
                        ${node.name}
                        <small class="text-muted ms-2">(${node.document_count} docs)</small>
                    </label>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', folderHTML);

        // Add children recursively
        if (hasChildren && level < 3) { // Limit depth to prevent too much nesting
            node.children.forEach(child => {
                displayFolderTree(child, container, level + 1);
            });
        }
    }

    function handleFolderSelection(checkbox) {
        // Handle parent/child folder selection logic if needed
        console.log('Folder selected:', checkbox.value, checkbox.checked);
    }

    function addManualFolder() {
        const pathInput = document.getElementById('manualFolderPath');
        const folderList = document.getElementById('manualFolderList');
        const path = pathInput.value.trim();

        if (!path) return;

        // Normalize path
        let normalizedPath = path.startsWith('/') ? path : '/' + path;

        // Check if already added
        const existing = folderList.querySelector(`[data-path="${normalizedPath}"]`);
        if (existing) {
            alert('This folder path is already added.');
            return;
        }

        // Add to manual list
        const folderItem = document.createElement('div');
        folderItem.className = 'folder-item-manual d-flex align-items-center justify-content-between border rounded p-2 mb-1';
        folderItem.setAttribute('data-path', normalizedPath);
        folderItem.innerHTML = `
            <div>
                <i class="fas fa-folder text-warning me-2"></i>
                <span>${normalizedPath}</span>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeManualFolder(this)">
                <i class="fas fa-times"></i>
            </button>
        `;

        folderList.appendChild(folderItem);
        pathInput.value = '';
    }

    function removeManualFolder(button) {
        button.closest('.folder-item-manual').remove();
    }

    function getSelectedFolders() {
        // Get folders from tree checkboxes
        const checkboxes = document.querySelectorAll('.folder-checkbox:checked');
        const treeFolders = Array.from(checkboxes).map(cb => cb.value);

        // Get manually added folders
        const manualItems = document.querySelectorAll('.folder-item-manual');
        const manualFolders = Array.from(manualItems).map(item => item.getAttribute('data-path'));

        // Combine and deduplicate
        const allFolders = [...treeFolders, ...manualFolders];
        return [...new Set(allFolders)];
    }

    function submitNewClient(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);
        const selectedFolders = getSelectedFolders();

        const clientData = {
            full_name: formData.get('full_name'),
            email: formData.get('email'),
            client_type: formData.get('client_type'),
            company_name: formData.get('company_name'),
            phone: formData.get('phone'),
            password: formData.get('password'),
            has_web_access: formData.get('has_web_access') === 'on',
            has_api_access: formData.get('has_api_access') === 'on',
            bot_access_enabled: formData.get('bot_access_enabled') === 'on',
            daily_question_limit: parseInt(formData.get('daily_question_limit')) || 50,
            assigned_folder_paths: selectedFolders
        };

        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';
        submitBtn.disabled = true;

        fetch('/api/owner/clients/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + localStorage.getItem('access_token')
            },
            body: JSON.stringify(clientData)
        })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.detail || 'Failed to create client');
                    });
                }
                return response.json();
            })
            .then(newClient => {
                showAlert(`Client "${newClient.full_name}" created successfully!`, 'success');
                resetAddClientForm();
                loadClientList();

                // Switch back to clients list tab
                const clientsTab = new bootstrap.Tab(document.getElementById('clients-tab'));
                clientsTab.show();
            })
            .catch(error => {
                console.error('Error creating client:', error);
                showAlert(`Error creating client: ${error.message}`, 'danger');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
    }

    function resetAddClientForm() {
        document.getElementById('addClientForm').reset();
        document.getElementById('companyNameGroup').style.display = 'none';

        // Uncheck all folder checkboxes
        document.querySelectorAll('.folder-checkbox').forEach(cb => {
            cb.checked = false;
        });

        // Clear manual folders
        const manualList = document.getElementById('manualFolderList');
        if (manualList) {
            manualList.innerHTML = '';
        }

        // Reset folder tree to initial state
        const treeContainer = document.getElementById('folderAccessTree');
        if (treeContainer) {
            treeContainer.innerHTML = `
                <div class="text-center py-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="loadFolderTree()">
                        <i class="fas fa-folder me-1"></i>Load Folders
                    </button>
                </div>
            `;
        }
    }

    function viewClientDetails(clientId) {
        selectedClientId = clientId;

        // Enable and switch to client details tab
        const detailsTab = document.getElementById('client-details-tab');
        detailsTab.disabled = false;
        const tab = new bootstrap.Tab(detailsTab);
        tab.show();

        loadClientDetails(clientId);
    }

    function loadClientDetails(clientId) {
        const content = document.getElementById('clientDetailsContent');
        content.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin"></i> Loading client details...</div>';

        fetch(`/api/owner/clients/${clientId}`, {
            headers: {
                'Authorization': 'Bearer ' + localStorage.getItem('access_token')
            }
        })
            .then(response => {
                if (!response.ok) throw new Error('Failed to load client details');
                return response.json();
            })
            .then(client => {
                displayClientDetails(client);
            })
            .catch(error => {
                console.error('Error loading client details:', error);
                content.innerHTML = `
                <div class="text-danger text-center py-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading client details: ${error.message}
                </div>
            `;
            });
    }

    function displayClientDetails(client) {
        const content = document.getElementById('clientDetailsContent');

        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Basic Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-4 text-muted">Name:</div>
                                <div class="col-8">${client.full_name}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-4 text-muted">Email:</div>
                                <div class="col-8">${client.email}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-4 text-muted">Type:</div>
                                <div class="col-8">
                                    <span class="badge bg-${client.client_type === 'business' ? 'info' : 'secondary'}">
                                        ${client.client_type}
                                    </span>
                                </div>
                            </div>
                            ${client.company_name ? `
                                <div class="row mb-2">
                                    <div class="col-4 text-muted">Company:</div>
                                    <div class="col-8">${client.company_name}</div>
                                </div>
                            ` : ''}
                            <div class="row mb-2">
                                <div class="col-4 text-muted">Phone:</div>
                                <div class="col-8">${client.phone || 'Not provided'}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-4 text-muted">Status:</div>
                                <div class="col-8">
                                    <span class="badge bg-${client.is_active ? 'success' : 'danger'}">
                                        ${client.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Access & Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-6 text-muted">Web Access:</div>
                                <div class="col-6">
                                    <span class="badge bg-${client.has_web_access ? 'success' : 'secondary'}">
                                        ${client.has_web_access ? 'Enabled' : 'Disabled'}
                                    </span>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-6 text-muted">API Access:</div>
                                <div class="col-6">
                                    <span class="badge bg-${client.has_api_access ? 'success' : 'secondary'}">
                                        ${client.has_api_access ? 'Enabled' : 'Disabled'}
                                    </span>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-6 text-muted">AI Bot:</div>
                                <div class="col-6">
                                    <span class="badge bg-${client.bot_access_enabled ? 'success' : 'secondary'}">
                                        ${client.bot_access_enabled ? 'Enabled' : 'Disabled'}
                                    </span>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-6 text-muted">Daily Limit:</div>
                                <div class="col-6">${client.daily_question_limit} questions</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-6 text-muted">Questions Asked:</div>
                                <div class="col-6">${client.total_questions_asked || 0}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-6 text-muted">Last Login:</div>
                                <div class="col-6">${client.last_login_at ? new Date(client.last_login_at).toLocaleString() : 'Never'}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Folder Access</h6>
                            <button class="btn btn-outline-primary btn-sm" onclick="editClientFolderAccess(${client.id})">
                                <i class="fas fa-edit me-1"></i>Edit Access
                            </button>
                        </div>
                        <div class="card-body">
                            ${client.assigned_folder_paths && client.assigned_folder_paths.length > 0 ?
                client.assigned_folder_paths.map(path =>
                    `<span class="badge bg-primary me-1 mb-1">${path}</span>`
                ).join('') :
                '<span class="text-muted">No folder access assigned</span>'
            }
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-end gap-2 mt-3">
                <button class="btn btn-outline-warning" onclick="editClient(${client.id})">
                    <i class="fas fa-edit me-1"></i>Edit Client
                </button>
                <button class="btn btn-outline-${client.is_active ? 'secondary' : 'success'}" 
                        onclick="toggleClientStatus(${client.id})">
                    <i class="fas fa-${client.is_active ? 'pause' : 'play'} me-1"></i>
                    ${client.is_active ? 'Deactivate' : 'Activate'}
                </button>
                <button class="btn btn-outline-danger" onclick="deleteClient(${client.id})">
                    <i class="fas fa-trash me-1"></i>Delete Client
                </button>
            </div>
        `;
    }

    function editClient(clientId) {
        showAlert('Edit client functionality coming soon!', 'info');
    }

    function editClientFolderAccess(clientId) {
        showAlert('Edit folder access functionality coming soon!', 'info');
    }

    function toggleClientStatus(clientId) {
        if (!confirm('Are you sure you want to change this client\'s status?')) {
            return;
        }

        fetch(`/api/owner/clients/${clientId}/toggle-status`, {
            method: 'PATCH',
            headers: {
                'Authorization': 'Bearer ' + localStorage.getItem('access_token')
            }
        })
            .then(response => {
                if (!response.ok) throw new Error('Failed to update client status');
                return response.json();
            })
            .then(updatedClient => {
                showAlert(`Client status updated to ${updatedClient.is_active ? 'Active' : 'Inactive'}`, 'success');
                loadClientList();

                // Refresh details if viewing this client
                if (selectedClientId === clientId) {
                    loadClientDetails(clientId);
                }
            })
            .catch(error => {
                console.error('Error updating client status:', error);
                showAlert(`Error updating client status: ${error.message}`, 'danger');
            });
    }

    function deleteClient(clientId) {
        const client = currentClients.find(c => c.id === clientId);
        if (!client) return;

        if (!confirm(`Are you sure you want to delete client "${client.full_name}"? This action cannot be undone.`)) {
            return;
        }

        fetch(`/api/owner/clients/${clientId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': 'Bearer ' + localStorage.getItem('access_token')
            }
        })
            .then(response => {
                if (!response.ok) throw new Error('Failed to delete client');
                return response.json();
            })
            .then(result => {
                showAlert(`Client "${client.full_name}" deleted successfully`, 'success');
                loadClientList();

                // If viewing this client's details, switch back to clients list
                if (selectedClientId === clientId) {
                    const clientsTab = new bootstrap.Tab(document.getElementById('clients-tab'));
                    clientsTab.show();
                    document.getElementById('client-details-tab').disabled = true;
                    selectedClientId = null;
                }
            })
            .catch(error => {
                console.error('Error deleting client:', error);
                showAlert(`Error deleting client: ${error.message}`, 'danger');
            });
    }

</script>
{% endblock %}