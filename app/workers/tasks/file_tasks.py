"""
File processing tasks for document management
"""
import os
import uuid
import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path
from celery import current_task
from app.workers.celery_app import celery_app

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name='process_dropbox_folder')
def process_dropbox_folder(self, owner_id: int, folder_path: str, selected_folders: List[str] = None):
    """
    Process files in a Dropbox folder (basic version without actual processing)
    This is a placeholder for the future full implementation
    """
    logger.info(f"Starting folder processing for owner {owner_id}, folder: {folder_path}")
    
    try:
        # Import here to avoid circular imports
        from app.core.database import get_db
        from app.models.owner import Owner
        from app.models.dropbox_integration import DropboxIntegration
        from app.services.dropbox_service import DropboxService
        
        # Get database session
        db = next(get_db())
        
        # Get owner and integration
        owner = db.query(Owner).filter(Owner.id == owner_id).first()
        if not owner:
            raise Exception(f"Owner with ID {owner_id} not found")
        
        integration = db.query(DropboxIntegration).filter(
            DropboxIntegration.owner_id == owner_id
        ).first()
        if not integration:
            raise Exception("No Dropbox integration found")
        
        # Initialize Dropbox service
        dropbox_service = DropboxService()
        
        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': 100, 'status': 'Connecting to Dropbox...'}
        )
        
        # Get folder contents
        folder_contents = dropbox_service.list_folder_contents(
            integration.access_token, 
            folder_path
        )
        
        total_files = len([item for item in folder_contents.items if not item.is_folder])
        processed_files = 0
        
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 10,
                'total': 100,
                'status': f'Found {total_files} files to process...'
            }
        )
        
        # Simulate file processing
        for i, item in enumerate(folder_contents.items):
            if not item.is_folder:
                # Simulate processing time
                time.sleep(0.1)  # Very quick for demo
                
                processed_files += 1
                progress = 10 + (80 * processed_files // total_files) if total_files > 0 else 90
                
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'current': progress,
                        'total': 100,
                        'status': f'Processing {item.name}... ({processed_files}/{total_files})'
                    }
                )
        
        # Final result
        result = {
            'owner_id': owner_id,
            'folder_path': folder_path,
            'total_files': total_files,
            'processed_files': processed_files,
            'task_id': self.request.id,
            'status': 'completed',
            'message': f'Successfully processed {processed_files} files from {folder_path}'
        }
        
        logger.info(f"Folder processing completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Folder processing failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'error': str(e), 'status': 'Failed to process folder'}
        )
        raise
    finally:
        if 'db' in locals():
            db.close()


@celery_app.task(bind=True, name='sync_dropbox_files')
def sync_dropbox_files(self, owner_id: int, incremental: bool = False):
    """
    Sync files from Dropbox (placeholder for future implementation)
    """
    logger.info(f"Starting Dropbox sync for owner {owner_id}, incremental: {incremental}")
    
    try:
        # Simulate sync process
        stages = [
            'Connecting to Dropbox...',
            'Fetching file list...',
            'Checking for changes...',
            'Downloading new files...',
            'Updating database...',
            'Generating thumbnails...',
            'Sync completed!'
        ]
        
        for i, stage in enumerate(stages):
            progress = (i + 1) * 100 // len(stages)
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': progress,
                    'total': 100,
                    'status': stage
                }
            )
            time.sleep(1)  # Simulate work
        
        result = {
            'owner_id': owner_id,
            'incremental': incremental,
            'files_synced': 42,  # Mock data
            'files_updated': 3,   # Mock data
            'task_id': self.request.id,
            'status': 'completed',
            'message': 'Dropbox sync completed successfully'
        }
        
        logger.info(f"Dropbox sync completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Dropbox sync failed: {str(e)}")
        raise


@celery_app.task(bind=True, name='generate_embeddings')
def generate_embeddings(self, document_ids: List[int]):
    """
    Generate embeddings for documents (placeholder for future implementation)
    """
    logger.info(f"Starting embedding generation for {len(document_ids)} documents")
    
    try:
        total_docs = len(document_ids)
        processed = 0
        
        for doc_id in document_ids:
            # Simulate embedding generation
            time.sleep(0.5)
            processed += 1
            
            progress = (processed * 100) // total_docs
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': progress,
                    'total': 100,
                    'status': f'Generating embeddings for document {doc_id}... ({processed}/{total_docs})'
                }
            )
        
        result = {
            'document_ids': document_ids,
            'embeddings_generated': processed,
            'task_id': self.request.id,
            'status': 'completed',
            'message': f'Generated embeddings for {processed} documents'
        }
        
        logger.info(f"Embedding generation completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Embedding generation failed: {str(e)}")
        raise


@celery_app.task(bind=True, name='download_dropbox_file')
def download_dropbox_file(self, owner_id: int, dropbox_file_path: str, force_redownload: bool = False):
    """
    Download a single file from Dropbox and store it with metadata
    
    Args:
        owner_id: Owner ID
        dropbox_file_path: Path to file in Dropbox
        force_redownload: Force download even if file exists
    
    Returns:
        Dictionary with download results
    """
    logger.info(f"Starting file download for owner {owner_id}, file: {dropbox_file_path}")
    
    try:
        # Import here to avoid circular imports
        from app.core.database import get_db
        from app.models.owner import Owner
        from app.models.dropbox_integration import DropboxIntegration
        from app.models.document import Document
        from app.services.dropbox_service import DropboxService
        from app.services.file_storage import FileStorageService
        
        # Get database session
        db = next(get_db())
        
        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={'current': 10, 'total': 100, 'status': 'Initializing...'}
        )
        
        # Get owner and integration
        owner = db.query(Owner).filter(Owner.id == owner_id).first()
        if not owner:
            raise Exception(f"Owner with ID {owner_id} not found")
        
        integration = db.query(DropboxIntegration).filter(
            DropboxIntegration.owner_id == owner_id
        ).first()
        if not integration:
            raise Exception("No Dropbox integration found")
        
        # Initialize services
        dropbox_service = DropboxService()
        storage_service = FileStorageService()
        
        self.update_state(
            state='PROGRESS',
            meta={'current': 20, 'total': 100, 'status': 'Getting file metadata...'}
        )
        
        # Get file metadata from Dropbox
        file_metadata = dropbox_service.get_file_metadata(
            integration.access_token,
            dropbox_file_path
        )
        
        # Check if document already exists
        existing_doc = db.query(Document).filter(
            Document.owner_id == owner_id,
            Document.dropbox_path == dropbox_file_path
        ).first()
        
        # Skip if file exists and not forcing redownload
        if existing_doc and not force_redownload:
            # Check if content has changed using content hash
            if existing_doc.dropbox_content_hash == file_metadata.get('content_hash'):
                logger.info(f"File {dropbox_file_path} unchanged, skipping download")
                return {
                    'owner_id': owner_id,
                    'dropbox_file_path': dropbox_file_path,
                    'status': 'skipped',
                    'reason': 'File unchanged',
                    'document_id': existing_doc.id
                }
        
        self.update_state(
            state='PROGRESS',
            meta={'current': 40, 'total': 100, 'status': 'Downloading file content...'}
        )
        
        # Download file content
        file_content = dropbox_service.download_file(
            integration.access_token,
            dropbox_file_path
        )
        
        self.update_state(
            state='PROGRESS',
            meta={'current': 60, 'total': 100, 'status': 'Storing file locally...'}
        )
        
        # Create or update document record
        if existing_doc:
            document = existing_doc
            # Update sync status
            document.sync_status = "syncing"
            document.sync_attempts += 1
        else:
            # Create new document record
            document = Document(
                owner_id=owner_id,
                dropbox_file_id=file_metadata['dropbox_file_id'],
                dropbox_path=dropbox_file_path,
                filename=file_metadata['name'],
                sync_status="syncing"
            )
            db.add(document)
            db.flush()  # Get the ID
        
        # Get storage path
        file_path = storage_service.get_file_path(
            owner_id, 
            document.id, 
            file_metadata['name']
        )
        
        # Save file to storage
        storage_result = storage_service.save_file(file_content, file_path)
        
        if not storage_result.get('storage_success'):
            raise Exception(f"Failed to store file: {storage_result.get('error')}")
        
        self.update_state(
            state='PROGRESS',
            meta={'current': 80, 'total': 100, 'status': 'Updating database...'}
        )
        
        # Update document metadata
        document.dropbox_rev = file_metadata.get('rev')
        document.file_size_bytes = file_metadata.get('size')
        document.content_hash = storage_result.get('content_hash')
        document.dropbox_content_hash = file_metadata.get('content_hash')
        
        # Parse dates
        if file_metadata.get('server_modified'):
            document.dropbox_modified_at = datetime.fromisoformat(
                file_metadata['server_modified'].replace('Z', '+00:00')
            )
        if file_metadata.get('client_modified'):
            document.dropbox_created_at = datetime.fromisoformat(
                file_metadata['client_modified'].replace('Z', '+00:00')
            )
        
        # Extract file extension and set MIME type
        if '.' in file_metadata['name']:
            document.file_extension = file_metadata['name'].split('.')[-1].lower()
        
        # Set sync completion
        document.sync_status = "completed"
        document.last_synced_at = datetime.utcnow()
        document.sync_error = None
        
        # Add processing metadata
        document.processing_metadata = {
            'downloaded_at': datetime.utcnow().isoformat(),
            'storage_path': str(file_path),
            'dropbox_metadata': file_metadata,
            'storage_metadata': storage_result
        }
        
        db.commit()
        
        result = {
            'owner_id': owner_id,
            'dropbox_file_path': dropbox_file_path,
            'document_id': document.id,
            'filename': document.filename,
            'file_size_bytes': document.file_size_bytes,
            'storage_path': str(file_path),
            'status': 'completed',
            'task_id': self.request.id,
            'message': f'Successfully downloaded and stored {document.filename}'
        }
        
        logger.info(f"File download completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"File download failed: {str(e)}")
        
        # Update document status if it exists
        if 'document' in locals() and document:
            document.mark_sync_failed(str(e))
            if 'db' in locals():
                db.commit()
        
        self.update_state(
            state='FAILURE',
            meta={'error': str(e), 'status': 'Failed to download file'}
        )
        raise
    finally:
        if 'db' in locals():
            db.close()


@celery_app.task(bind=True, name='download_folder_files')
def download_folder_files(self, owner_id: int, folder_paths: List[str], file_types: List[str] = None, max_files: int = None):
    """
    Download all files from specified Dropbox folders
    
    Args:
        owner_id: Owner ID
        folder_paths: List of folder paths to process
        file_types: Optional file types to filter (e.g., ['pdf', 'docx'])
        max_files: Maximum number of files to process
    
    Returns:
        Dictionary with batch download results
    """
    logger.info(f"Starting batch file download for owner {owner_id}, folders: {folder_paths}")
    
    job_id = str(uuid.uuid4())
    
    try:
        # Import here to avoid circular imports
        from app.core.database import get_db
        from app.models.owner import Owner
        from app.models.dropbox_integration import DropboxIntegration
        from app.models.sync_job import SyncJob
        from app.services.dropbox_service import DropboxService
        
        # Get database session
        db = next(get_db())
        
        # Create sync job record
        sync_job = SyncJob(
            job_id=job_id,
            job_type='folder_download',
            owner_id=owner_id,
            status='running',
            job_config={
                'folder_paths': folder_paths,
                'file_types': file_types,
                'max_files': max_files
            }
        )
        sync_job.start_job()
        db.add(sync_job)
        db.commit()
        
        # Update task state
        self.update_state(
            state='PROGRESS',
            meta={'current': 5, 'total': 100, 'status': 'Initializing batch download...'}
        )
        
        # Get owner and integration
        owner = db.query(Owner).filter(Owner.id == owner_id).first()
        if not owner:
            raise Exception(f"Owner with ID {owner_id} not found")
        
        integration = db.query(DropboxIntegration).filter(
            DropboxIntegration.owner_id == owner_id
        ).first()
        if not integration:
            raise Exception("No Dropbox integration found")
        
        # Initialize Dropbox service
        dropbox_service = DropboxService()
        
        # Collect all files from all folders
        all_files = []
        
        self.update_state(
            state='PROGRESS',
            meta={'current': 10, 'total': 100, 'status': 'Scanning folders for files...'}
        )
        
        for folder_path in folder_paths:
            try:
                folder_files = dropbox_service.list_files_recursive(
                    integration.access_token,
                    folder_path,
                    file_types
                )
                all_files.extend(folder_files)
            except Exception as e:
                logger.warning(f"Failed to list files in folder {folder_path}: {str(e)}")
                sync_job.files_failed += 1
        
        # Limit files if specified
        if max_files and len(all_files) > max_files:
            all_files = all_files[:max_files]
        
        total_files = len(all_files)
        sync_job.files_total = total_files
        db.commit()
        
        if total_files == 0:
            sync_job.complete_job(success=True, summary={'message': 'No files found to download'})
            db.commit()
            return {
                'job_id': job_id,
                'status': 'completed',
                'files_total': 0,
                'files_processed': 0,
                'message': 'No files found to download'
            }
        
        logger.info(f"Found {total_files} files to download")
        
        # Process files
        processed_files = 0
        failed_files = 0
        skipped_files = 0
        
        for i, file_info in enumerate(all_files):
            try:
                progress = 10 + (80 * i // total_files)
                
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'current': progress,
                        'total': 100,
                        'status': f'Downloading {file_info["name"]}... ({i+1}/{total_files})'
                    }
                )
                
                # Download individual file
                download_result = download_dropbox_file.delay(
                    owner_id,
                    file_info['path_display'],
                    force_redownload=False
                ).get(timeout=300)  # 5 minute timeout per file
                
                if download_result['status'] == 'completed':
                    processed_files += 1
                elif download_result['status'] == 'skipped':
                    skipped_files += 1
                else:
                    failed_files += 1
                
                # Update sync job progress
                sync_job.update_file_progress(processed_files, failed_files, skipped_files)
                db.commit()
                
            except Exception as e:
                logger.error(f"Failed to download file {file_info['name']}: {str(e)}")
                failed_files += 1
                sync_job.update_file_progress(processed_files, failed_files, skipped_files)
                db.commit()
        
        # Complete sync job
        success = failed_files == 0
        summary = {
            'files_total': total_files,
            'files_processed': processed_files,
            'files_failed': failed_files,
            'files_skipped': skipped_files,
            'folder_paths': folder_paths,
            'file_types': file_types
        }
        
        sync_job.complete_job(success=success, summary=summary)
        db.commit()
        
        result = {
            'job_id': job_id,
            'owner_id': owner_id,
            'status': 'completed' if success else 'completed_with_errors',
            'files_total': total_files,
            'files_processed': processed_files,
            'files_failed': failed_files,
            'files_skipped': skipped_files,
            'folder_paths': folder_paths,
            'task_id': self.request.id,
            'message': f'Batch download completed: {processed_files} processed, {failed_files} failed, {skipped_files} skipped'
        }
        
        logger.info(f"Batch file download completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Batch file download failed: {str(e)}")
        
        # Mark sync job as failed
        if 'sync_job' in locals() and sync_job:
            sync_job.fail_job(str(e))
            if 'db' in locals():
                db.commit()
        
        self.update_state(
            state='FAILURE',
            meta={'error': str(e), 'status': 'Failed to download files'}
        )
        raise
    finally:
        if 'db' in locals():
            db.close()
