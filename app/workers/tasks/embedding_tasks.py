"""
Celery tasks for background embedding processing
"""
import logging
from typing import List, Dict, Any
from celery import current_task
from sqlalchemy.orm import Session

from ...workers.celery_app import celery_app
from ...core.database import SessionLocal
from ...models import Document, DocumentEmbedding, Owner
from ...services.enhanced_embedding_service import EnhancedEmbeddingService

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name='process_document_embeddings')
def process_document_embeddings(
    self, 
    document_id: int, 
    force_regenerate: bool = False,
    chunk_size: int = None,
    chunk_overlap: int = None,
    embedding_model: str = None
):
    """
    Process embeddings for a single document
    
    Args:
        document_id: ID of the document to process
        force_regenerate: Force regeneration of existing embeddings
        chunk_size: Custom chunk size for text splitting
        chunk_overlap: Custom overlap size for text chunks
        embedding_model: Custom embedding model to use
    """
    logger.info(f"Starting embedding processing for document {document_id}")
    
    db = SessionLocal()
    try:
        # Initialize embedding service
        if embedding_model:
            # Parse model string (e.g., "sentence_transformers/all-MiniLM-L6-v2")
            provider, model_name = embedding_model.split("/", 1)
            embedding_service = EnhancedEmbeddingService(provider=provider, model_name=model_name)
        else:
            embedding_service = EnhancedEmbeddingService()
        
        # Update task status
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 0,
                'total': 100,
                'status': f'Processing document {document_id}...'
            }
        )
        
        # Process the document
        result = embedding_service.process_document_for_embeddings(
            document_id=document_id,
            db=db,
            force_regenerate=force_regenerate,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        
        # Update task status
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 100,
                'total': 100,
                'status': 'Processing completed'
            }
        )
        
        logger.info(f"Embedding processing completed for document {document_id}: {result}")
        
        # Add task ID to result
        result['task_id'] = self.request.id
        
        return result
        
    except Exception as e:
        logger.error(f"Embedding processing failed for document {document_id}: {str(e)}")
        
        # Update task status with error
        self.update_state(
            state='FAILURE',
            meta={
                'error': str(e),
                'document_id': document_id
            }
        )
        
        raise
        
    finally:
        db.close()


@celery_app.task(bind=True, name='process_batch_embeddings')
def process_batch_embeddings(
    self, 
    document_ids: List[int], 
    force_regenerate: bool = False,
    embedding_model: str = None,
    owner_id: int = None
):
    """
    Process embeddings for multiple documents in batch
    
    Args:
        document_ids: List of document IDs to process
        force_regenerate: Force regeneration of existing embeddings
        embedding_model: Custom embedding model to use
        owner_id: Owner ID for logging and filtering
    """
    logger.info(f"Starting batch embedding processing for {len(document_ids)} documents")
    
    db = SessionLocal()
    try:
        # Initialize embedding service
        if embedding_model:
            provider, model_name = embedding_model.split("/", 1)
            embedding_service = EnhancedEmbeddingService(provider=provider, model_name=model_name)
        else:
            embedding_service = EnhancedEmbeddingService()
        
        total_docs = len(document_ids)
        processed_count = 0
        failed_count = 0
        results = []
        
        for i, doc_id in enumerate(document_ids):
            try:
                # Update progress
                progress = int((i / total_docs) * 100)
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'current': i,
                        'total': total_docs,
                        'status': f'Processing document {doc_id}... ({i+1}/{total_docs})',
                        'progress': progress,
                        'processed': processed_count,
                        'failed': failed_count
                    }
                )
                
                # Process the document
                result = embedding_service.process_document_for_embeddings(
                    document_id=doc_id,
                    db=db,
                    force_regenerate=force_regenerate
                )
                
                results.append(result)
                
                if result['success']:
                    processed_count += 1
                else:
                    failed_count += 1
                    
            except Exception as e:
                logger.error(f"Failed to process embeddings for document {doc_id}: {e}")
                failed_count += 1
                results.append({
                    'document_id': doc_id,
                    'success': False,
                    'error': str(e)
                })
        
        # Final result
        final_result = {
            'task_id': self.request.id,
            'total_documents': total_docs,
            'processed_successfully': processed_count,
            'failed': failed_count,
            'results': results,
            'owner_id': owner_id
        }
        
        logger.info(f"Batch embedding processing completed: {processed_count} successful, {failed_count} failed")
        
        return final_result
        
    except Exception as e:
        logger.error(f"Batch embedding processing failed: {str(e)}")
        
        self.update_state(
            state='FAILURE',
            meta={
                'error': str(e),
                'document_ids': document_ids
            }
        )
        
        raise
        
    finally:
        db.close()


@celery_app.task(bind=True, name='process_all_owner_embeddings')
def process_all_owner_embeddings(
    self, 
    owner_id: int, 
    force_regenerate: bool = False,
    only_missing: bool = True,
    embedding_model: str = None
):
    """
    Process embeddings for all documents belonging to an owner
    
    Args:
        owner_id: ID of the owner
        force_regenerate: Force regeneration of existing embeddings
        only_missing: Only process documents without embeddings
        embedding_model: Custom embedding model to use
    """
    logger.info(f"Starting full embedding processing for owner {owner_id}")
    
    db = SessionLocal()
    try:
        # Get all documents for the owner
        query = db.query(Document).filter(
            Document.owner_id == owner_id,
            Document.extracted_text.isnot(None),
            Document.extracted_text != ""
        )
        
        if only_missing and not force_regenerate:
            query = query.filter(Document.embeddings_generated == False)
        
        documents = query.all()
        document_ids = [doc.id for doc in documents]
        
        if not document_ids:
            return {
                'task_id': self.request.id,
                'message': 'No documents found for processing',
                'owner_id': owner_id,
                'total_documents': 0
            }
        
        # Update initial status
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 0,
                'total': len(document_ids),
                'status': f'Starting processing for {len(document_ids)} documents...',
                'owner_id': owner_id
            }
        )
        
        # Initialize embedding service
        if embedding_model:
            provider, model_name = embedding_model.split("/", 1)
            embedding_service = EnhancedEmbeddingService(provider=provider, model_name=model_name)
        else:
            embedding_service = EnhancedEmbeddingService()
        
        processed_count = 0
        failed_count = 0
        results = []
        
        for i, doc_id in enumerate(document_ids):
            try:
                # Update progress
                progress = int((i / len(document_ids)) * 100)
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'current': i,
                        'total': len(document_ids),
                        'status': f'Processing document {doc_id}... ({i+1}/{len(document_ids)})',
                        'progress': progress,
                        'processed': processed_count,
                        'failed': failed_count,
                        'owner_id': owner_id
                    }
                )
                
                # Process the document
                result = embedding_service.process_document_for_embeddings(
                    document_id=doc_id,
                    db=db,
                    force_regenerate=force_regenerate
                )
                
                results.append(result)
                
                if result['success']:
                    processed_count += 1
                else:
                    failed_count += 1
                    
            except Exception as e:
                logger.error(f"Failed to process embeddings for document {doc_id}: {e}")
                failed_count += 1
                results.append({
                    'document_id': doc_id,
                    'success': False,
                    'error': str(e)
                })
        
        # Final result
        final_result = {
            'task_id': self.request.id,
            'owner_id': owner_id,
            'total_documents': len(document_ids),
            'processed_successfully': processed_count,
            'failed': failed_count,
            'force_regenerate': force_regenerate,
            'only_missing': only_missing,
            'results': results
        }
        
        logger.info(f"Full owner embedding processing completed for owner {owner_id}: {processed_count} successful, {failed_count} failed")
        
        return final_result
        
    except Exception as e:
        logger.error(f"Full owner embedding processing failed for owner {owner_id}: {str(e)}")
        
        self.update_state(
            state='FAILURE',
            meta={
                'error': str(e),
                'owner_id': owner_id
            }
        )
        
        raise
        
    finally:
        db.close()


@celery_app.task(bind=True, name='cleanup_embeddings')
def cleanup_embeddings(self, owner_id: int = None):
    """
    Clean up orphaned and failed embeddings
    
    Args:
        owner_id: Limit cleanup to specific owner (optional)
    """
    logger.info(f"Starting embedding cleanup for owner {owner_id if owner_id else 'all'}")
    
    db = SessionLocal()
    try:
        # Initialize embedding service
        embedding_service = EnhancedEmbeddingService()
        
        # Update task status
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 0,
                'total': 100,
                'status': 'Cleaning up embeddings...'
            }
        )
        
        # Perform cleanup
        cleanup_stats = embedding_service.cleanup_embeddings(db, owner_id)
        
        # Update task status
        self.update_state(
            state='PROGRESS',
            meta={
                'current': 100,
                'total': 100,
                'status': 'Cleanup completed'
            }
        )
        
        result = {
            'task_id': self.request.id,
            'owner_id': owner_id,
            'cleanup_statistics': cleanup_stats
        }
        
        logger.info(f"Embedding cleanup completed: {cleanup_stats}")
        
        return result
        
    except Exception as e:
        logger.error(f"Embedding cleanup failed: {str(e)}")
        
        self.update_state(
            state='FAILURE',
            meta={
                'error': str(e),
                'owner_id': owner_id
            }
        )
        
        raise
        
    finally:
        db.close()


@celery_app.task(bind=True, name='regenerate_embeddings_for_model')
def regenerate_embeddings_for_model(
    self,
    new_embedding_model: str,
    owner_id: int = None,
    document_ids: List[int] = None
):
    """
    Regenerate embeddings using a different model
    
    Args:
        new_embedding_model: New embedding model to use (format: "provider/model_name")
        owner_id: Limit to specific owner (optional)
        document_ids: Limit to specific documents (optional)
    """
    logger.info(f"Starting embedding regeneration with model {new_embedding_model}")
    
    db = SessionLocal()
    try:
        # Parse new model
        provider, model_name = new_embedding_model.split("/", 1)
        embedding_service = EnhancedEmbeddingService(provider=provider, model_name=model_name)
        
        # Get documents to process
        if document_ids:
            documents = db.query(Document).filter(
                Document.id.in_(document_ids),
                Document.extracted_text.isnot(None)
            ).all()
        elif owner_id:
            documents = db.query(Document).filter(
                Document.owner_id == owner_id,
                Document.extracted_text.isnot(None)
            ).all()
        else:
            documents = db.query(Document).filter(
                Document.extracted_text.isnot(None)
            ).all()
        
        total_docs = len(documents)
        processed_count = 0
        failed_count = 0
        
        for i, document in enumerate(documents):
            try:
                # Update progress
                progress = int((i / total_docs) * 100)
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'current': i,
                        'total': total_docs,
                        'status': f'Regenerating embeddings for document {document.id}... ({i+1}/{total_docs})',
                        'progress': progress,
                        'processed': processed_count,
                        'failed': failed_count
                    }
                )
                
                # Process with new model
                result = embedding_service.process_document_for_embeddings(
                    document_id=document.id,
                    db=db,
                    force_regenerate=True  # Always force regeneration
                )
                
                if result['success']:
                    processed_count += 1
                else:
                    failed_count += 1
                    
            except Exception as e:
                logger.error(f"Failed to regenerate embeddings for document {document.id}: {e}")
                failed_count += 1
        
        result = {
            'task_id': self.request.id,
            'new_embedding_model': new_embedding_model,
            'owner_id': owner_id,
            'total_documents': total_docs,
            'processed_successfully': processed_count,
            'failed': failed_count
        }
        
        logger.info(f"Embedding regeneration completed: {processed_count} successful, {failed_count} failed")
        
        return result
        
    except Exception as e:
        logger.error(f"Embedding regeneration failed: {str(e)}")
        
        self.update_state(
            state='FAILURE',
            meta={
                'error': str(e),
                'new_embedding_model': new_embedding_model
            }
        )
        
        raise
        
    finally:
        db.close()
