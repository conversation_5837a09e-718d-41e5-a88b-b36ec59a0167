"""
Test tasks for verifying Celery setup
"""
import time
import logging
from celery import current_task
from app.workers.celery_app import celery_app

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name='test_basic_task')
def test_basic_task(self, message: str = "Hello from Celery!"):
    """
    Basic test task to verify Celery is working
    """
    logger.info(f"Starting test task with message: {message}")
    
    # Update task progress
    self.update_state(
        state='PROGRESS',
        meta={'current': 0, 'total': 3, 'status': 'Starting test...'}
    )
    time.sleep(1)
    
    self.update_state(
        state='PROGRESS',
        meta={'current': 1, 'total': 3, 'status': 'Processing...'}
    )
    time.sleep(1)
    
    self.update_state(
        state='PROGRESS',
        meta={'current': 2, 'total': 3, 'status': 'Almost done...'}
    )
    time.sleep(1)
    
    result = {
        'message': message,
        'task_id': self.request.id,
        'status': 'completed',
        'timestamp': time.time()
    }
    
    logger.info(f"Test task completed: {result}")
    return result


@celery_app.task(bind=True, name='test_long_running_task')
def test_long_running_task(self, duration: int = 10):
    """
    Long-running test task to simulate file processing
    """
    logger.info(f"Starting long-running task for {duration} seconds")
    
    for i in range(duration):
        # Update progress every second
        self.update_state(
            state='PROGRESS',
            meta={
                'current': i + 1,
                'total': duration,
                'status': f'Working... ({i + 1}/{duration})'
            }
        )
        time.sleep(1)
    
    result = {
        'duration': duration,
        'task_id': self.request.id,
        'status': 'completed',
        'message': f'Long-running task completed in {duration} seconds'
    }
    
    logger.info(f"Long-running task completed: {result}")
    return result


@celery_app.task(bind=True, name='test_error_handling')
def test_error_handling(self, should_fail: bool = True):
    """
    Test task for error handling and retries
    """
    logger.info(f"Starting error handling test, should_fail: {should_fail}")
    
    if should_fail:
        # Simulate a failure
        raise Exception("This is a test error for retry mechanism")
    
    return {
        'message': 'Task completed successfully',
        'task_id': self.request.id,
        'status': 'completed'
    }


@celery_app.task(bind=True, name='test_database_task')
def test_database_task(self, owner_id: int):
    """
    Test task that interacts with the database
    """
    logger.info(f"Testing database task for owner {owner_id}")
    
    try:
        # Import here to avoid circular imports
        from app.core.database import get_db
        from app.models.owner import Owner
        
        # Get database session
        db = next(get_db())
        
        # Query owner
        owner = db.query(Owner).filter(Owner.id == owner_id).first()
        
        if not owner:
            raise Exception(f"Owner with ID {owner_id} not found")
        
        result = {
            'owner_id': owner_id,
            'owner_email': owner.email,
            'task_id': self.request.id,
            'status': 'completed',
            'message': 'Database access successful'
        }
        
        logger.info(f"Database task completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Database task failed: {str(e)}")
        raise
    finally:
        if 'db' in locals():
            db.close()
