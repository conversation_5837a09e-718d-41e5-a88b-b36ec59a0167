"""
Celery application configuration for background task processing
"""
from celery import Celery
import os
from app.core.config import settings

# Create Celery instance
celery_app = Celery(
    "ca_firm_worker",
    broker=f"redis://{settings.redis_host}:{settings.redis_port}/{settings.redis_db}",
    backend=f"redis://{settings.redis_host}:{settings.redis_port}/{settings.redis_db}",
    include=[
        'app.workers.tasks.file_tasks',
        'app.workers.tasks.test_tasks',
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task execution settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Task routing and delivery
    task_routes={
        'app.workers.tasks.file_tasks.*': {'queue': 'file_processing'},
        'app.workers.tasks.test_tasks.*': {'queue': 'default'},
    },
    
    # Task result settings
    result_expires=3600,  # Results expire after 1 hour
    task_track_started=True,
    task_send_sent_event=True,
    
    # Worker settings
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=False,
    worker_log_format='[%(asctime)s: %(levelname)s/%(processName)s] %(message)s',
    worker_task_log_format='[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s',
    
    # Retry settings
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    
    # Error handling
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
)

# Configure logging
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Auto-discover tasks
celery_app.autodiscover_tasks([
    'app.workers.tasks'
])

if __name__ == '__main__':
    celery_app.start()
