"""
Pydantic schemas for Owner model
"""
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime
from typing import Optional


class OwnerBase(BaseModel):
    """Base schema for Owner"""
    email: EmailStr
    full_name: str = Field(..., min_length=2, max_length=255)
    firm_name: str = Field(..., min_length=2, max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    address: Optional[str] = None
    timezone: str = Field(default="UTC", max_length=50)


class OwnerCreate(OwnerBase):
    """Schema for creating a new owner"""
    password: str = Field(..., min_length=8, max_length=100)


class OwnerUpdate(BaseModel):
    """Schema for updating owner information"""
    full_name: Optional[str] = Field(None, min_length=2, max_length=255)
    firm_name: Optional[str] = Field(None, min_length=2, max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    address: Optional[str] = None
    timezone: Optional[str] = Field(None, max_length=50)


class OwnerResponse(OwnerBase):
    """Schema for owner response (excludes password)"""
    id: int
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class OwnerLogin(BaseModel):
    """Schema for owner login"""
    email: EmailStr
    password: str


class OwnerLoginResponse(BaseModel):
    """Schema for login response"""
    access_token: str
    token_type: str = "bearer"
    owner: OwnerResponse
    
    class Config:
        from_attributes = True
