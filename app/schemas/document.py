"""
Pydantic schemas for Document model
"""
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, Dict, Any, List


class DocumentBase(BaseModel):
    """Base document schema"""
    filename: str = Field(..., max_length=500)
    file_extension: Optional[str] = Field(None, max_length=10)
    file_size_bytes: Optional[int] = None
    mime_type: Optional[str] = Field(None, max_length=100)
    dropbox_path: str = Field(..., max_length=1000)


class DocumentCreate(DocumentBase):
    """Schema for creating a document"""
    dropbox_file_id: str = Field(..., max_length=255)
    dropbox_rev: Optional[str] = Field(None, max_length=255)
    content_hash: Optional[str] = Field(None, max_length=64)
    dropbox_content_hash: Optional[str] = Field(None, max_length=64)
    dropbox_created_at: Optional[datetime] = None
    dropbox_modified_at: Optional[datetime] = None


class DocumentUpdate(BaseModel):
    """Schema for updating a document"""
    filename: Optional[str] = Field(None, max_length=500)
    file_size_bytes: Optional[int] = None
    mime_type: Optional[str] = Field(None, max_length=100)
    content_hash: Optional[str] = Field(None, max_length=64)
    dropbox_content_hash: Optional[str] = Field(None, max_length=64)
    dropbox_modified_at: Optional[datetime] = None
    extracted_text: Optional[str] = None
    text_summary: Optional[str] = None
    text_extracted: Optional[bool] = None
    embeddings_generated: Optional[bool] = None
    is_processed: Optional[bool] = None
    searchable: Optional[bool] = None
    sync_status: Optional[str] = Field(None, max_length=50)
    sync_error: Optional[str] = None
    processing_metadata: Optional[Dict[str, Any]] = None


class DocumentResponse(DocumentBase):
    """Schema for document response"""
    id: int
    owner_id: int
    dropbox_file_id: str
    dropbox_rev: Optional[str]
    content_hash: Optional[str]
    dropbox_content_hash: Optional[str]
    
    # Timestamps
    dropbox_created_at: Optional[datetime]
    dropbox_modified_at: Optional[datetime]
    last_synced_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    # Processing status
    is_processed: bool
    text_extracted: bool
    embeddings_generated: bool
    searchable: bool
    
    # Sync status
    sync_status: str
    sync_error: Optional[str]
    sync_attempts: int
    
    # Derived properties
    file_type_category: str
    is_text_extractable: bool
    
    class Config:
        from_attributes = True


class DocumentSummary(BaseModel):
    """Lightweight document summary for lists"""
    id: int
    filename: str
    file_extension: Optional[str]
    file_size_bytes: Optional[int]
    sync_status: str
    is_processed: bool
    searchable: bool
    dropbox_modified_at: Optional[datetime]
    created_at: datetime
    file_type_category: str


class DocumentSearch(BaseModel):
    """Schema for document search requests"""
    query: str = Field(..., min_length=1, max_length=500)
    file_types: Optional[List[str]] = Field(None, description="Filter by file types")
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    processed_only: bool = Field(default=True, description="Only search processed documents")
    limit: int = Field(default=20, ge=1, le=100)
    offset: int = Field(default=0, ge=0)


class DocumentSearchResult(BaseModel):
    """Schema for document search results"""
    document: DocumentSummary
    relevance_score: float = Field(..., ge=0.0, le=1.0)
    matched_content: Optional[str] = Field(None, description="Snippet of matched content")
    
    
class DocumentStats(BaseModel):
    """Schema for document statistics"""
    total_documents: int
    processed_documents: int
    searchable_documents: int
    total_size_bytes: int
    file_type_breakdown: Dict[str, int]
    sync_status_breakdown: Dict[str, int]
    recent_syncs: int  # Documents synced in last 24h
    
    
class DocumentContent(BaseModel):
    """Schema for document content"""
    document_id: int
    filename: str
    extracted_text: Optional[str]
    text_summary: Optional[str]
    processing_metadata: Optional[Dict[str, Any]]
    can_extract_text: bool
