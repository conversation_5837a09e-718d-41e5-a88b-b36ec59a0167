"""
Pydantic schemas for Dropbox integration
"""
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List


class DropboxAuthURL(BaseModel):
    """Schema for Dropbox authorization URL response"""
    auth_url: str = Field(..., description="URL to redirect user for Dropbox authorization")
    instructions: str = Field(..., description="Instructions for manual code entry")


class DropboxManualAuth(BaseModel):
    """Schema for manual Dropbox authorization code entry"""
    auth_code: str = Field(..., description="Authorization code from Dropbox (manual entry)")


class DropboxCallback(BaseModel):
    """Schema for handling Dropbox OAuth callback"""
    code: str = Field(..., description="Authorization code from Dropbox")
    state: str = Field(..., description="State parameter for verification")


class DropboxAccountInfo(BaseModel):
    """Schema for Dropbox account information"""
    account_id: str
    email: str
    name: str
    country: Optional[str] = None
    locale: Optional[str] = None


class DropboxIntegrationSettings(BaseModel):
    """Schema for updating Dropbox integration settings"""
    sync_enabled: Optional[bool] = None
    sync_folder_path: Optional[str] = Field(None, max_length=500)
    auto_sync: Optional[bool] = None
    sync_frequency_minutes: Optional[int] = Field(None, ge=5, le=1440)  # 5 minutes to 24 hours
    allowed_extensions: Optional[str] = Field(None, max_length=200)
    max_file_size_mb: Optional[int] = Field(None, ge=1, le=500)


class DropboxIntegrationResponse(BaseModel):
    """Schema for Dropbox integration response"""
    id: int
    owner_id: int
    account_id: Optional[str]
    email: Optional[str]
    name: Optional[str]
    sync_enabled: bool
    sync_folder_path: str
    auto_sync: bool
    sync_frequency_minutes: int
    allowed_extensions: str
    max_file_size_mb: int
    last_sync_at: Optional[str]
    sync_status: str
    last_error: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class DropboxFolderItem(BaseModel):
    """Schema for Dropbox folder/file items"""
    name: str
    path: str
    is_folder: bool
    size: Optional[int] = None  # Size in bytes, None for folders
    modified: Optional[str] = None  # ISO timestamp
    file_type: Optional[str] = None  # Extension for files


class DropboxFolderContents(BaseModel):
    """Schema for Dropbox folder contents"""
    path: str
    items: List[DropboxFolderItem]
    has_more: bool = False
    cursor: Optional[str] = None


class DropboxSyncStatus(BaseModel):
    """Schema for sync status response"""
    integration_id: int
    sync_status: str  # pending, syncing, completed, error
    last_sync_at: Optional[str]
    last_error: Optional[str]
    files_synced: int = 0
    files_pending: int = 0
    sync_progress_percent: float = 0.0


class DropboxConnectionTest(BaseModel):
    """Schema for connection test response"""
    connected: bool
    account_info: Optional[DropboxAccountInfo] = None
    error: Optional[str] = None
    permissions: List[str] = []
