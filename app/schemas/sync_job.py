"""
Pydantic schemas for SyncJob model
"""
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum


class JobType(str, Enum):
    """Enumeration of job types"""
    FULL_SYNC = "full_sync"
    INCREMENTAL_SYNC = "incremental_sync"
    SINGLE_FILE = "single_file"
    MANUAL_SYNC = "manual_sync"


class JobStatus(str, Enum):
    """Enumeration of job statuses"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SyncJobCreate(BaseModel):
    """Schema for creating a sync job"""
    job_type: JobType
    document_id: Optional[int] = None
    job_config: Optional[Dict[str, Any]] = None
    max_retries: int = Field(default=3, ge=0, le=10)


class SyncJobUpdate(BaseModel):
    """Schema for updating a sync job"""
    status: Optional[JobStatus] = None
    progress_percentage: Optional[int] = Field(None, ge=0, le=100)
    step_current: Optional[str] = Field(None, max_length=100)
    step_completed: Optional[int] = Field(None, ge=0)
    files_processed: Optional[int] = Field(None, ge=0)
    files_failed: Optional[int] = Field(None, ge=0)
    files_skipped: Optional[int] = Field(None, ge=0)
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    result_summary: Optional[Dict[str, Any]] = None


class SyncJobResponse(BaseModel):
    """Schema for sync job response"""
    id: int
    job_id: str
    job_type: str
    status: str
    progress_percentage: int
    owner_id: int
    document_id: Optional[int]
    
    # Timing
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    estimated_completion: Optional[datetime]
    
    # Progress tracking
    step_current: Optional[str]
    step_total: int
    step_completed: int
    
    # File processing
    files_total: int
    files_processed: int
    files_failed: int
    files_skipped: int
    
    # Error handling
    error_message: Optional[str]
    retry_count: int
    max_retries: int
    
    # Configuration and results
    job_config: Optional[Dict[str, Any]]
    result_summary: Optional[Dict[str, Any]]
    
    # Computed properties
    duration_seconds: Optional[float]
    is_running: bool
    is_completed: bool
    success_rate: float
    
    class Config:
        from_attributes = True


class SyncJobSummary(BaseModel):
    """Lightweight sync job summary for lists"""
    id: int
    job_id: str
    job_type: str
    status: str
    progress_percentage: int
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    files_total: int
    files_processed: int
    files_failed: int
    error_message: Optional[str]
    duration_seconds: Optional[float]


class SyncJobStats(BaseModel):
    """Schema for sync job statistics"""
    total_jobs: int
    active_jobs: int
    completed_jobs: int
    failed_jobs: int
    success_rate: float
    avg_duration_seconds: Optional[float]
    total_files_processed: int
    jobs_by_type: Dict[str, int]
    jobs_by_status: Dict[str, int]
    recent_jobs: List[SyncJobSummary]


class SyncJobProgress(BaseModel):
    """Schema for real-time job progress updates"""
    job_id: str
    status: str
    progress_percentage: int
    step_current: Optional[str]
    files_processed: int
    files_total: int
    estimated_completion: Optional[datetime]
    last_updated: datetime


class SyncJobRequest(BaseModel):
    """Schema for manual sync job requests"""
    job_type: JobType = JobType.MANUAL_SYNC
    folder_path: Optional[str] = Field(None, description="Specific folder to sync (optional)")
    file_types: Optional[List[str]] = Field(None, description="File types to include (optional)")
    force_resync: bool = Field(default=False, description="Force re-sync of existing files")
    
    
class SyncJobCancel(BaseModel):
    """Schema for cancelling a sync job"""
    reason: Optional[str] = Field(None, max_length=500, description="Reason for cancellation")


class BatchSyncResult(BaseModel):
    """Schema for batch sync operation results"""
    job_id: str
    total_files: int
    successful: int
    failed: int
    skipped: int
    errors: List[Dict[str, Any]]
    duration_seconds: float
    summary: str
