"""
Pydantic schemas for Client-related models
"""
from pydantic import BaseModel, EmailStr, Field, validator
from datetime import datetime
from typing import Optional, List, Dict, Any
import re


# ============================================================================
# Client Schemas
# ============================================================================

class ClientBase(BaseModel):
    """Base schema for Client"""
    email: EmailStr
    full_name: str = Field(..., min_length=2, max_length=255)
    company_name: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    
    # Address information
    address_line1: Optional[str] = Field(None, max_length=500)
    address_line2: Optional[str] = Field(None, max_length=500)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    country: Optional[str] = Field(None, max_length=100)
    
    # Business relationship details
    client_type: str = Field(default="individual", pattern="^(individual|business)$")
    industry: Optional[str] = Field(None, max_length=100)
    annual_revenue: Optional[float] = Field(None, ge=0)
    tax_id: Optional[str] = Field(None, max_length=50)
    registration_number: Optional[str] = Field(None, max_length=100)
    
    # Service details
    retainer_amount: Optional[float] = Field(None, ge=0)
    billing_frequency: str = Field(default="monthly", pattern="^(monthly|quarterly|annual)$")


class ClientCreate(ClientBase):
    """Schema for creating a new client"""
    password: str = Field(..., min_length=8, max_length=100)
    
    # Access settings
    has_web_access: bool = Field(default=True)
    has_api_access: bool = Field(default=False)
    
    # Document access settings
    assigned_folder_paths: List[str] = Field(default_factory=list)
    allowed_document_types: List[str] = Field(default_factory=list)
    allowed_file_extensions: List[str] = Field(default_factory=list)
    
    # Service details
    services_subscribed: List[str] = Field(default_factory=list)
    
    # Notification settings
    email_notifications: bool = Field(default=True)
    sms_notifications: bool = Field(default=False)
    notification_frequency: str = Field(default="weekly", pattern="^(daily|weekly|monthly)$")
    
    # Security settings
    two_factor_enabled: bool = Field(default=False)
    session_timeout_minutes: int = Field(default=30, ge=5, le=1440)  # 5 min to 24 hours
    
    # AI Bot settings
    bot_access_enabled: bool = Field(default=True)
    daily_question_limit: int = Field(default=50, ge=1, le=1000)
    search_scope: str = Field(default="assigned_documents_only", pattern="^(assigned_documents_only|all_documents)$")
    include_archived_documents: bool = Field(default=False)
    search_history_retention_days: int = Field(default=90, ge=1, le=365)
    
    @validator('password')
    def validate_password(cls, v):
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Za-z]', v):
            raise ValueError('Password must contain at least one letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one number')
        return v
    
    @validator('phone')
    def validate_phone(cls, v):
        """Validate phone number format"""
        if v and not re.match(r'^[\+\-\(\)\s\d]+$', v):
            raise ValueError('Invalid phone number format')
        return v
    
    @validator('assigned_folder_paths')
    def validate_folder_paths(cls, v):
        """Validate folder paths format"""
        for path in v:
            if not path.startswith('/'):
                raise ValueError('Folder paths must start with /')
        return v


class ClientUpdate(BaseModel):
    """Schema for updating client information"""
    full_name: Optional[str] = Field(None, min_length=2, max_length=255)
    company_name: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    
    # Address information
    address_line1: Optional[str] = Field(None, max_length=500)
    address_line2: Optional[str] = Field(None, max_length=500)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    country: Optional[str] = Field(None, max_length=100)
    
    # Business relationship details
    industry: Optional[str] = Field(None, max_length=100)
    annual_revenue: Optional[float] = Field(None, ge=0)
    tax_id: Optional[str] = Field(None, max_length=50)
    registration_number: Optional[str] = Field(None, max_length=100)
    
    # Access settings
    has_web_access: Optional[bool] = None
    has_api_access: Optional[bool] = None
    
    # Document access settings
    assigned_folder_paths: Optional[List[str]] = None
    allowed_document_types: Optional[List[str]] = None
    allowed_file_extensions: Optional[List[str]] = None
    
    # Service details
    services_subscribed: Optional[List[str]] = None
    retainer_amount: Optional[float] = Field(None, ge=0)
    billing_frequency: Optional[str] = Field(None, pattern="^(monthly|quarterly|annual)$")
    
    # Account settings
    is_active: Optional[bool] = None
    account_end_date: Optional[datetime] = None
    
    # Notification settings
    email_notifications: Optional[bool] = None
    sms_notifications: Optional[bool] = None
    notification_frequency: Optional[str] = Field(None, pattern="^(daily|weekly|monthly)$")
    
    # Security settings
    two_factor_enabled: Optional[bool] = None
    session_timeout_minutes: Optional[int] = Field(None, ge=5, le=1440)
    
    # AI Bot settings
    bot_access_enabled: Optional[bool] = None
    daily_question_limit: Optional[int] = Field(None, ge=1, le=1000)
    search_scope: Optional[str] = Field(None, pattern="^(assigned_documents_only|all_documents)$")
    include_archived_documents: Optional[bool] = None
    search_history_retention_days: Optional[int] = Field(None, ge=1, le=365)


class ClientResponse(ClientBase):
    """Schema for client response (excludes sensitive data)"""
    id: int
    owner_id: int
    is_active: bool
    is_verified: bool
    
    # Access settings
    has_web_access: bool
    has_api_access: bool
    
    # Document access settings
    assigned_folder_paths: List[str]
    allowed_document_types: List[str]
    allowed_file_extensions: List[str]
    
    # Service details
    services_subscribed: List[str]
    
    # Account settings
    account_start_date: datetime
    account_end_date: Optional[datetime]
    
    # Notification settings
    email_notifications: bool
    sms_notifications: bool
    notification_frequency: str
    
    # Security settings
    two_factor_enabled: bool
    session_timeout_minutes: int
    last_login_at: Optional[datetime]
    
    # AI Bot settings
    bot_access_enabled: bool
    daily_question_limit: int
    search_scope: str
    include_archived_documents: bool
    search_history_retention_days: int
    
    # Usage tracking
    total_questions_asked: int
    last_question_at: Optional[datetime]
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ClientSummary(BaseModel):
    """Schema for client summary (for lists and dropdowns)"""
    id: int
    email: str
    full_name: str
    company_name: Optional[str]
    client_type: str
    is_active: bool
    has_web_access: bool
    has_api_access: bool
    total_questions_asked: int
    last_login_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True


class ClientLogin(BaseModel):
    """Schema for client login"""
    email: EmailStr
    password: str


class ClientLoginResponse(BaseModel):
    """Schema for client login response"""
    access_token: str
    token_type: str = "bearer"
    client: ClientResponse
    
    class Config:
        from_attributes = True


class ClientPasswordChange(BaseModel):
    """Schema for client password change"""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=100)
    confirm_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """Validate new password strength"""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Za-z]', v):
            raise ValueError('Password must contain at least one letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one number')
        return v
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """Validate that passwords match"""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v


# ============================================================================
# Client API Key Schemas
# ============================================================================

class ClientApiKeyBase(BaseModel):
    """Base schema for Client API Key"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    rate_limit_per_hour: int = Field(default=100, ge=1, le=10000)
    allowed_endpoints: Optional[List[str]] = Field(default_factory=list)
    expires_at: Optional[datetime] = None


class ClientApiKeyCreate(ClientApiKeyBase):
    """Schema for creating a new client API key"""
    pass


class ClientApiKeyUpdate(BaseModel):
    """Schema for updating client API key"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    rate_limit_per_hour: Optional[int] = Field(None, ge=1, le=10000)
    allowed_endpoints: Optional[List[str]] = None
    expires_at: Optional[datetime] = None
    is_active: Optional[bool] = None


class ClientApiKeyResponse(ClientApiKeyBase):
    """Schema for client API key response"""
    id: int
    client_id: int
    api_key: str  # Only shown once during creation, then masked
    is_active: bool
    last_used_at: Optional[datetime]
    total_requests: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ClientApiKeyListResponse(BaseModel):
    """Schema for client API key list (with masked keys)"""
    id: int
    name: str
    description: Optional[str]
    api_key_preview: str  # Masked version like "sk-****"
    is_active: bool
    rate_limit_per_hour: int
    last_used_at: Optional[datetime]
    total_requests: int
    expires_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True


# ============================================================================
# Chat Session Schemas
# ============================================================================

class ChatQuestionRequest(BaseModel):
    """Schema for client chat question"""
    question: str = Field(..., min_length=1, max_length=2000)
    session_id: Optional[str] = None
    include_sources: bool = Field(default=True)


class ChatSourceInfo(BaseModel):
    """Schema for chat response source information"""
    document_id: int
    document_name: str
    document_path: str
    relevance_score: float
    chunk_preview: str


class ChatResponse(BaseModel):
    """Schema for chat response"""
    answer: str
    sources: List[ChatSourceInfo]
    confidence_score: Optional[float]
    response_time_ms: int
    search_results_count: int
    session_id: str
    suggestion: Optional[str] = None
    error: Optional[str] = None


class ChatSessionResponse(BaseModel):
    """Schema for chat session history"""
    id: int
    session_id: str
    question: str
    answer: str
    created_at: datetime
    confidence_score: Optional[float]
    sources: List[int]  # Document IDs
    user_feedback: Optional[str]
    response_time_ms: Optional[int]
    
    class Config:
        from_attributes = True


class ChatFeedbackRequest(BaseModel):
    """Schema for chat feedback"""
    feedback: str = Field(..., pattern="^(helpful|not_helpful|partially_helpful)$")
    comment: Optional[str] = Field(None, max_length=1000)


class ChatHistoryRequest(BaseModel):
    """Schema for chat history request"""
    limit: int = Field(default=50, ge=1, le=500)
    session_id: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


# ============================================================================
# Document Embedding Schemas
# ============================================================================

class DocumentEmbeddingResponse(BaseModel):
    """Schema for document embedding response"""
    id: int
    document_id: int
    chunk_index: int
    text_chunk: str
    embedding_model: str
    vector_dimension: int
    processing_status: str
    processed_at: datetime
    is_searchable: bool
    relevance_score: Optional[float]
    
    class Config:
        from_attributes = True


class EmbeddingSearchRequest(BaseModel):
    """Schema for embedding search request"""
    query: str = Field(..., min_length=1, max_length=1000)
    limit: int = Field(default=5, ge=1, le=50)
    similarity_threshold: float = Field(default=0.6, ge=0.0, le=1.0)
    document_types: Optional[List[str]] = None
    include_archived: bool = Field(default=False)


class EmbeddingSearchResult(BaseModel):
    """Schema for embedding search result"""
    document_id: int
    document_name: str
    document_path: str
    chunk_index: int
    text_chunk: str
    similarity_score: float
    document_type: Optional[str]
    document_created_at: datetime


class EmbeddingStatsResponse(BaseModel):
    """Schema for embedding statistics"""
    total_documents: int
    documents_with_embeddings: int
    total_embeddings: int
    processing_pending: int
    processing_failed: int
    last_processed_at: Optional[datetime]


# ============================================================================
# Bulk Operations Schemas
# ============================================================================

class BulkClientCreate(BaseModel):
    """Schema for bulk client creation"""
    clients: List[ClientCreate] = Field(..., min_items=1, max_items=100)
    send_welcome_email: bool = Field(default=True)
    default_folder_access: List[str] = Field(default_factory=list)


class BulkClientResponse(BaseModel):
    """Schema for bulk client creation response"""
    created: List[ClientResponse]
    failed: List[Dict[str, Any]]
    total_attempted: int
    total_created: int
    total_failed: int


class ClientFolderAccessUpdate(BaseModel):
    """Schema for updating client folder access"""
    client_ids: List[int] = Field(..., min_items=1)
    folder_paths: List[str]
    operation: str = Field(..., pattern="^(add|remove|replace)$")


class ClientUsageStats(BaseModel):
    """Schema for client usage statistics"""
    client_id: int
    total_questions: int
    questions_this_month: int
    questions_today: int
    average_confidence_score: Optional[float]
    most_used_documents: List[str]
    last_active_date: Optional[datetime]
    session_count: int


# ============================================================================
# Search and Filter Schemas
# ============================================================================

class ClientSearchRequest(BaseModel):
    """Schema for client search and filtering"""
    search_term: Optional[str] = None
    client_type: Optional[str] = Field(None, pattern="^(individual|business)$")
    is_active: Optional[bool] = None
    has_web_access: Optional[bool] = None
    has_api_access: Optional[bool] = None
    services: Optional[List[str]] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    last_login_after: Optional[datetime] = None
    sort_by: str = Field(default="created_at", pattern="^(created_at|last_login_at|full_name|email|total_questions_asked)$")
    sort_order: str = Field(default="desc", pattern="^(asc|desc)$")
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)


class ClientSearchResponse(BaseModel):
    """Schema for client search response"""
    clients: List[ClientSummary]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool
