"""
FastAPI application entry point for CA Firm Document Management System
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
try:
    from .api.database import router as database_router
    from .api.owner.auth import router as auth_router
    from .api.owner.dashboard import router as dashboard_router
    from .api.owner.dropbox import router as dropbox_router
    from .api.owner.documents import router as documents_router
    from .api.owner.tasks import router as tasks_router
    from .api.owner.clients import router as clients_router
    from .api.owner.client_documents import router as client_documents_router
    from .api.owner.embeddings import router as embeddings_router
    from .core.database import create_tables
    DATABASE_AVAILABLE = True
except ImportError as e:
    print(f"Database functionality disabled: {e}")
    DATABASE_AVAILABLE = False

# Create FastAPI instance
app = FastAPI(
    title="CA Firm Document Management API",
    description="AI-powered document management and search system for CA firms",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware for frontend access
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Include routers if available
if DATABASE_AVAILABLE:
    app.include_router(database_router, prefix="/api")
    app.include_router(auth_router, prefix="/api/owner")
    app.include_router(dashboard_router, prefix="/api/owner")
    app.include_router(dropbox_router, prefix="/api/owner")
    app.include_router(documents_router, prefix="/api/owner")
    app.include_router(tasks_router, prefix="/api/owner")
    app.include_router(clients_router, prefix="/api/owner")
    app.include_router(client_documents_router, prefix="/api/owner")
    app.include_router(embeddings_router, prefix="/api/owner")

# Startup event to create database tables if available
if DATABASE_AVAILABLE:
    @app.on_event("startup")
    async def startup_event():
        """Create database tables on startup"""
        create_tables()

# Health check endpoint
@app.get("/")
async def root():
    return {
        "message": "CA Firm Document Management API",
        "status": "running",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "ca-firm-api"}

@app.get("/api/database/status")
async def database_status():
    """Database status endpoint (placeholder for now)"""
    return {
        "status": "pending",
        "message": "Database integration in progress",
        "current_task": "Task 2: Database Configuration",
        "features": [
            "✅ Basic FastAPI setup complete",
            "🔄 Database models created", 
            "⏳ SQLAlchemy integration pending",
            "⏳ Database connection testing pending"
        ]
    }

# Basic welcome page
@app.get("/welcome", response_class=HTMLResponse)
async def welcome():
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>CA Firm Document Management</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; }
            .feature { margin: 20px 0; padding: 20px; background: #ecf0f1; border-radius: 5px; }
            .api-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; }
            .api-link:hover { background: #2980b9; }
            .status { color: #27ae60; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🏢 CA Firm Document Management System</h1>
            <p><span class="status">✅ System Status: Online</span></p>
            
            <div class="feature">
                <h3>🔗 Dropbox Integration</h3>
                <p>Automatically sync and index documents from your Dropbox account for AI-powered search.</p>
            </div>
            
            <div class="feature">
                <h3>🔍 AI Document Search</h3>
                <p>Powerful semantic search across all your client documents using advanced AI embeddings.</p>
            </div>
            
            <div class="feature">
                <h3>🔑 Client API Access</h3>
                <p>Secure API endpoints for clients to search and access their documents programmatically.</p>
            </div>
            
            <div class="feature">
                <h3>📊 Analytics & Management</h3>
                <p>Track usage, manage clients, and monitor document processing in real-time.</p>
            </div>
            
            <div style="text-align: center; margin-top: 40px;">
                <a href="/docs" class="api-link">📖 API Documentation</a>
                <a href="/redoc" class="api-link">📋 ReDoc</a>
            </div>
            
            <p style="text-align: center; color: #7f8c8d; margin-top: 40px;">
                Built with FastAPI, PostgreSQL, and AI-powered search
            </p>
        </div>
    </body>
    </html>
    """
    return html_content

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
