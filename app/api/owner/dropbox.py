"""
Dropbox integration API endpoints for owners
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from typing import Optional
from urllib.parse import unquote
import logging

from ...core.database import get_db
from ...core.auth import get_current_owner
from ...models import Owner, DropboxIntegration
from ...schemas.dropbox import (
    DropboxAuthURL,
    DropboxManualAuth,
    DropboxCallback,
    DropboxIntegrationResponse,
    DropboxIntegrationSettings,
    DropboxFolderContents,
    DropboxConnectionTest,
    DropboxSyncStatus
)
from ...services.dropbox_service import dropbox_service

router = APIRouter(prefix="/dropbox", tags=["Dropbox Integration"])
logger = logging.getLogger(__name__)

# Store OAuth states temporarily (in production, use Redis or database)
# Using a simple file-based storage to persist across server restarts
import json
import os
from datetime import datetime, timedelta

OAUTH_STATES_FILE = "temp_oauth_states.json"

def save_oauth_state(state: str, owner_id: int):
    """Save OAuth state to persistent storage"""
    try:
        # Load existing states
        states = {}
        if os.path.exists(OAUTH_STATES_FILE):
            with open(OAUTH_STATES_FILE, 'r') as f:
                states = json.load(f)
        
        # Clean expired states (older than 1 hour)
        current_time = datetime.now()
        states = {k: v for k, v in states.items() 
                 if datetime.fromisoformat(v['timestamp']) > current_time - timedelta(hours=1)}
        
        # Add new state
        states[state] = {
            'owner_id': owner_id,
            'timestamp': current_time.isoformat()
        }
        
        # Save back to file
        with open(OAUTH_STATES_FILE, 'w') as f:
            json.dump(states, f)
            
    except Exception as e:
        logger.error(f"Error saving OAuth state: {e}")

def get_oauth_state(state: str) -> Optional[int]:
    """Get and remove OAuth state from persistent storage"""
    try:
        if not os.path.exists(OAUTH_STATES_FILE):
            return None
            
        with open(OAUTH_STATES_FILE, 'r') as f:
            states = json.load(f)
        
        # Check if state exists
        if state not in states:
            return None
            
        owner_id = states[state]['owner_id']
        
        # Remove used state
        del states[state]
        
        # Save back to file
        with open(OAUTH_STATES_FILE, 'w') as f:
            json.dump(states, f)
            
        return owner_id
        
    except Exception as e:
        logger.error(f"Error getting OAuth state: {e}")
        return None

def list_oauth_states() -> list:
    """List all stored OAuth states for debugging"""
    try:
        if not os.path.exists(OAUTH_STATES_FILE):
            return []
            
        with open(OAUTH_STATES_FILE, 'r') as f:
            states = json.load(f)
            
        return list(states.keys())
    except Exception as e:
        logger.error(f"Error listing OAuth states: {e}")
        return []


@router.get("/auth-url", response_model=DropboxAuthURL, summary="Get Dropbox authorization URL")
async def get_auth_url(
    current_owner: Owner = Depends(get_current_owner)
):
    """
    Generate Dropbox OAuth authorization URL for manual code entry
    """
    try:
        auth_url = dropbox_service.get_authorization_url()
        
        instructions = (
            "1. Click the authorization URL below\n"
            "2. Log in to your Dropbox account\n"
            "3. Click 'Allow' to grant access\n"
            "4. Copy the authorization code shown\n"
            "5. Paste it in the form below"
        )
        
        logger.info(f"Generated auth URL for owner {current_owner.id}: {auth_url}")
        
        return DropboxAuthURL(auth_url=auth_url, instructions=instructions)
        
    except Exception as e:
        logger.error(f"Failed to generate authorization URL for owner {current_owner.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate authorization URL: {str(e)}"
        )


@router.post("/connect", summary="Connect Dropbox with manual authorization code")
async def connect_dropbox(
    auth_data: DropboxManualAuth,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Connect Dropbox using manually entered authorization code
    """
    try:
        logger.info(f"Manual Dropbox connection attempt for owner {current_owner.id}")
        
        # Exchange code for token
        token_data = dropbox_service.exchange_code_for_token(auth_data.auth_code)
        
        # Get account info
        account_info = dropbox_service.get_account_info(token_data['access_token'])
        
        # Check if integration already exists
        db_integration = db.query(DropboxIntegration).filter(DropboxIntegration.owner_id == current_owner.id).first()
        
        if db_integration:
            # Update existing integration
            db_integration.access_token = token_data['access_token']
            db_integration.refresh_token = token_data.get('refresh_token')
            db_integration.account_id = token_data['account_id']
            db_integration.email = account_info.email
            db_integration.name = account_info.name
            db_integration.sync_status = "connected"
            db_integration.last_error = None
        else:
            # Create new integration
            db_integration = DropboxIntegration(
                owner_id=current_owner.id,
                access_token=token_data['access_token'],
                refresh_token=token_data.get('refresh_token'),
                account_id=token_data['account_id'],
                email=account_info.email,
                name=account_info.name,
                sync_status="connected"
            )
            db.add(db_integration)
        
        db.commit()
        db.refresh(db_integration)
        
        logger.info(f"Dropbox successfully connected for owner {current_owner.id}, account: {account_info.email}")
        
        return {
            "success": True,
            "message": "Dropbox connected successfully!",
            "account_info": {
                "email": account_info.email,
                "name": account_info.name,
                "account_id": account_info.account_id
            }
        }
        
    except Exception as e:
        logger.error(f"Dropbox manual connection failed for owner {current_owner.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to connect Dropbox: {str(e)}"
        )


@router.get("/callback", summary="Handle Dropbox OAuth callback")
async def dropbox_callback(
    code: str = Query(..., description="Authorization code from Dropbox"),
    state: str = Query(..., description="State parameter for verification"),
    db: Session = Depends(get_db)
):
    """
    Handle the OAuth callback from Dropbox
    """
    try:
        # URL decode the state parameter from Dropbox callback
        decoded_state = unquote(state)
        
        # Debug logging
        logger.info(f"Callback received - State: '{state}', Decoded: '{decoded_state}'")
        logger.info(f"Available oauth_states: {list_oauth_states()}")
        
        # Try to get owner ID from persistent storage (try both encoded and decoded versions)
        owner_id = get_oauth_state(state) or get_oauth_state(decoded_state)
        
        if owner_id is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid or expired state parameter. Received: '{state}', Available: {list_oauth_states()}"
            )
        
        # Use the decoded state for token exchange
        actual_state = decoded_state
        
        # Get owner
        owner = db.query(Owner).filter(Owner.id == owner_id).first()
        if not owner:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Owner not found"
            )
        
        # Exchange code for token
        token_data = dropbox_service.exchange_code_for_token(code, actual_state)
        
        # Get account info
        account_info = dropbox_service.get_account_info(token_data['access_token'])
        
        # Create/update integration
        integration = dropbox_service.create_integration(
            db=db,
            owner=owner,
            access_token=token_data['access_token'],
            refresh_token=token_data.get('refresh_token'),
            account_info=account_info
        )
        
        # Redirect to dashboard with success message
        return RedirectResponse(
            url="/api/owner/dashboard/?dropbox=connected",
            status_code=status.HTTP_302_FOUND
        )
        
    except HTTPException:
        raise
    except Exception as e:
        # Redirect to dashboard with error
        return RedirectResponse(
            url=f"/api/owner/dashboard/?dropbox=error&message={str(e)[:100]}",
            status_code=status.HTTP_302_FOUND
        )


@router.get("/status", response_model=Optional[DropboxIntegrationResponse], summary="Get integration status")
async def get_integration_status(
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Get the current Dropbox integration status for the owner
    """
    integration = db.query(DropboxIntegration).filter(
        DropboxIntegration.owner_id == current_owner.id
    ).first()
    
    if not integration:
        return None
    
    return integration


@router.post("/connect", summary="Start Dropbox connection process" )
async def connect_dropbox(
    current_owner: Owner = Depends(get_current_owner)
):
    """
    Start the Dropbox connection process by redirecting to OAuth
    """
    try:
        auth_url, state = dropbox_service.get_authorization_url()
        
        # Store state temporarily
        save_oauth_state(state, current_owner.id)
        
        return {"redirect_url": auth_url}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start connection: {str(e)}"
        )


@router.delete("/disconnect", summary="Disconnect Dropbox integration")
async def disconnect_dropbox(
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Disconnect and remove Dropbox integration
    """
    integration = db.query(DropboxIntegration).filter(
        DropboxIntegration.owner_id == current_owner.id
    ).first()
    
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No Dropbox integration found"
        )
    
    try:
        dropbox_service.disconnect_integration(db, integration)
        return {"message": "Dropbox integration disconnected successfully"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to disconnect: {str(e)}"
        )


@router.get("/test-connection", response_model=DropboxConnectionTest, summary="Test Dropbox connection")
async def test_connection(
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Test the current Dropbox connection
    """
    integration = db.query(DropboxIntegration).filter(
        DropboxIntegration.owner_id == current_owner.id
    ).first()
    
    if not integration:
        return DropboxConnectionTest(
            connected=False,
            error="No Dropbox integration found"
        )
    
    # Test connection with automatic token refresh if needed
    try:
        connection_result = dropbox_service.test_connection(integration.access_token)
        return connection_result
        
    except Exception as e:
        # Check if it's an auth error and try to refresh token
        error_str = str(e).lower()
        if 'expired_access_token' in error_str or 'auth' in error_str:
            if integration.refresh_token:
                try:
                    logger.info(f"Access token expired for owner {current_owner.id}, attempting refresh for test connection")
                    
                    # Refresh the token
                    token_data = dropbox_service.refresh_access_token(integration.refresh_token)
                    
                    # Update the integration with new tokens
                    integration.access_token = token_data['access_token']
                    integration.refresh_token = token_data['refresh_token']
                    db.commit()
                    
                    logger.info(f"Successfully refreshed token for owner {current_owner.id}")
                    
                    # Retry the connection test with new token
                    connection_result = dropbox_service.test_connection(integration.access_token)
                    return connection_result
                    
                except Exception as refresh_error:
                    logger.error(f"Token refresh failed for owner {current_owner.id}: {refresh_error}")
                    return DropboxConnectionTest(
                        connected=False,
                        error="Dropbox access token expired and refresh failed. Please reconnect your account."
                    )
            else:
                return DropboxConnectionTest(
                    connected=False,
                    error="Dropbox access token expired. Please reconnect your account."
                )
        else:
            return DropboxConnectionTest(
                connected=False,
                error=f"Connection test failed: {str(e)}"
            )


@router.get("/folders", response_model=DropboxFolderContents, summary="List folder contents")
async def list_folder_contents(
    folder_path: str = Query("", description="Folder path to list (empty for root)"),
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    List contents of a Dropbox folder
    """
    integration = db.query(DropboxIntegration).filter(
        DropboxIntegration.owner_id == current_owner.id
    ).first()
    
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No Dropbox integration found"
        )
    
    try:
        folder_contents = dropbox_service.list_folder_contents(
            integration.access_token, 
            folder_path
        )
        return folder_contents
        
    except Exception as e:
        # Check if it's an auth error and try to refresh token
        error_str = str(e).lower()
        if 'expired_access_token' in error_str or 'auth' in error_str:
            if integration.refresh_token:
                try:
                    logger.info(f"Access token expired for owner {current_owner.id}, attempting refresh")
                    
                    # Refresh the token
                    token_data = dropbox_service.refresh_access_token(integration.refresh_token)
                    
                    # Update the integration with new tokens
                    integration.access_token = token_data['access_token']
                    integration.refresh_token = token_data['refresh_token']
                    db.commit()
                    
                    logger.info(f"Successfully refreshed token for owner {current_owner.id}")
                    
                    # Retry the folder listing with new token
                    folder_contents = dropbox_service.list_folder_contents(
                        integration.access_token, 
                        folder_path
                    )
                    return folder_contents
                    
                except Exception as refresh_error:
                    logger.error(f"Token refresh failed for owner {current_owner.id}: {refresh_error}")
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Dropbox access token expired and refresh failed. Please reconnect your account."
                    )
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Dropbox access token expired. Please reconnect your account."
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list folder contents: {str(e)}"
            )


@router.put("/settings", response_model=DropboxIntegrationResponse, summary="Update integration settings")
async def update_settings(
    settings: DropboxIntegrationSettings,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Update Dropbox integration settings
    """
    integration = db.query(DropboxIntegration).filter(
        DropboxIntegration.owner_id == current_owner.id
    ).first()
    
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No Dropbox integration found"
        )
    
    try:
        # Update settings
        update_data = settings.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(integration, field, value)
        
        db.commit()
        db.refresh(integration)
        
        return integration
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update settings: {str(e)}"
        )


@router.get("/folders/stats", summary="Get folder statistics with file counts")
async def get_folder_stats(
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Get statistics for all top-level folders (file counts, sizes)
    """
    integration = db.query(DropboxIntegration).filter(
        DropboxIntegration.owner_id == current_owner.id
    ).first()
    
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No Dropbox integration found"
        )
    
    try:
        # First get the folder list
        folder_contents = dropbox_service.list_folder_contents(integration.access_token, "")
        folder_stats = {}
        
        # Get stats for each folder
        for item in folder_contents.items:
            if item.is_folder:
                try:
                    stats = dropbox_service.get_folder_stats(integration.access_token, item.path)
                    folder_stats[item.path] = {
                        'name': item.name,
                        'path': item.path,
                        'file_count': stats['file_count'],
                        'folder_count': stats['folder_count'],
                        'total_size': stats['total_size'],
                        'total_items': stats['total_items']
                    }
                except Exception as e:
                    logger.warning(f"Failed to get stats for folder {item.path}: {e}")
                    folder_stats[item.path] = {
                        'name': item.name,
                        'path': item.path,
                        'file_count': 0,
                        'folder_count': 0,
                        'total_size': 0,
                        'total_items': 0
                    }
        
        return {"folder_stats": folder_stats}
        
    except Exception as e:
        # Check if it's an auth error and try to refresh token
        error_str = str(e).lower()
        if 'expired_access_token' in error_str or 'auth' in error_str:
            if integration.refresh_token:
                try:
                    logger.info(f"Access token expired for owner {current_owner.id}, attempting refresh for folder stats")
                    
                    # Refresh the token
                    token_data = dropbox_service.refresh_access_token(integration.refresh_token)
                    
                    # Update the integration with new tokens
                    integration.access_token = token_data['access_token']
                    integration.refresh_token = token_data['refresh_token']
                    db.commit()
                    
                    logger.info(f"Successfully refreshed token for owner {current_owner.id}")
                    
                    # Retry getting folder stats with new token
                    folder_contents = dropbox_service.list_folder_contents(integration.access_token, "")
                    folder_stats = {}
                    
                    for item in folder_contents.items:
                        if item.is_folder:
                            try:
                                stats = dropbox_service.get_folder_stats(integration.access_token, item.path)
                                folder_stats[item.path] = {
                                    'name': item.name,
                                    'path': item.path,
                                    'file_count': stats['file_count'],
                                    'folder_count': stats['folder_count'],
                                    'total_size': stats['total_size'],
                                    'total_items': stats['total_items']
                                }
                            except Exception as folder_error:
                                logger.warning(f"Failed to get stats for folder {item.path}: {folder_error}")
                                folder_stats[item.path] = {
                                    'name': item.name,
                                    'path': item.path,
                                    'file_count': 0,
                                    'folder_count': 0,
                                    'total_size': 0,
                                    'total_items': 0
                                }
                    
                    return {"folder_stats": folder_stats}
                    
                except Exception as refresh_error:
                    logger.error(f"Token refresh failed for owner {current_owner.id}: {refresh_error}")
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Dropbox access token expired and refresh failed. Please reconnect your account."
                    )
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Dropbox access token expired. Please reconnect your account."
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get folder statistics: {str(e)}"
            )


@router.get("/ca-firm-files", summary="Get files from CA_Firm folder for embedding")
async def get_ca_firm_files(
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Get all files from the CA_Firm folder for embedding selection
    """
    integration = db.query(DropboxIntegration).filter(
        DropboxIntegration.owner_id == current_owner.id
    ).first()

    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No Dropbox integration found"
        )

    try:
        # List files in CA_Firm folder specifically
        ca_firm_path = "/CA_Firm"
        
        # Get files recursively from CA_Firm folder
        files = dropbox_service.list_files_recursive(
            integration.access_token,
            ca_firm_path,
            file_types=['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx', 'csv', 'ppt', 'pptx']  # Document types for embedding
        )
        
        # Format files for frontend display
        formatted_files = []
        for file_info in files:
            formatted_files.append({
                'dropbox_file_id': file_info['dropbox_file_id'],
                'name': file_info['name'],
                'path': file_info['path_display'],
                'size': file_info['size'],
                'size_formatted': format_file_size(file_info['size']),
                'modified': file_info['server_modified'],
                'file_extension': file_info['name'].split('.')[-1].lower() if '.' in file_info['name'] else '',
                'is_downloadable': file_info['is_downloadable']
            })
        
        return {
            'folder_path': ca_firm_path,
            'total_files': len(formatted_files),
            'files': formatted_files
        }

    except Exception as e:
        # Check if it's an auth error and try to refresh token
        error_str = str(e).lower()
        if 'expired_access_token' in error_str or 'auth' in error_str:
            if integration.refresh_token:
                try:
                    logger.info(f"Access token expired for owner {current_owner.id}, attempting refresh for CA_Firm files")
                    
                    # Refresh the token
                    token_data = dropbox_service.refresh_access_token(integration.refresh_token)
                    
                    # Update the integration with new tokens
                    integration.access_token = token_data['access_token']
                    integration.refresh_token = token_data['refresh_token']
                    db.commit()
                    
                    logger.info(f"Successfully refreshed token for owner {current_owner.id}")
                    
                    # Retry getting CA_Firm files with new token
                    files = dropbox_service.list_files_recursive(
                        integration.access_token,
                        ca_firm_path,
                        file_types=['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx', 'csv', 'ppt', 'pptx']
                    )
                    
                    formatted_files = []
                    for file_info in files:
                        formatted_files.append({
                            'dropbox_file_id': file_info['dropbox_file_id'],
                            'name': file_info['name'],
                            'path': file_info['path_display'],
                            'size': file_info['size'],
                            'size_formatted': format_file_size(file_info['size']),
                            'modified': file_info['server_modified'],
                            'file_extension': file_info['name'].split('.')[-1].lower() if '.' in file_info['name'] else '',
                            'is_downloadable': file_info['is_downloadable']
                        })
                    
                    return {
                        'folder_path': ca_firm_path,
                        'total_files': len(formatted_files),
                        'files': formatted_files
                    }
                    
                except Exception as refresh_error:
                    logger.error(f"Token refresh failed for owner {current_owner.id}: {refresh_error}")
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Dropbox access token expired and refresh failed. Please reconnect your account."
                    )
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Dropbox access token expired. Please reconnect your account."
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get CA_Firm files: {str(e)}"
            )


@router.get("/download-file", summary="Download file from Dropbox")
async def download_file_from_dropbox(
    file_path: str,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Download a file from Dropbox and return it as a response
    """
    integration = db.query(DropboxIntegration).filter(
        DropboxIntegration.owner_id == current_owner.id
    ).first()

    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No Dropbox integration found"
        )

    try:
        # Download file content
        file_content = dropbox_service.download_file(
            integration.access_token,
            file_path
        )
        
        # Get file metadata for proper filename and content type
        file_metadata = dropbox_service.get_file_metadata(
            integration.access_token,
            file_path
        )
        
        # Determine content type based on file extension
        filename = file_metadata['name']
        file_extension = filename.split('.')[-1].lower() if '.' in filename else ''
        
        content_type_map = {
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'csv': 'text/csv',
            'txt': 'text/plain',
            'default': 'application/octet-stream'
        }
        
        content_type = content_type_map.get(file_extension, content_type_map['default'])
        
        # Return file as download
        from fastapi.responses import Response
        return Response(
            content=file_content,
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename=\"{filename}\"",
                "Content-Length": str(len(file_content))
            }
        )

    except Exception as e:
        # Check if it's an auth error and try to refresh token
        error_str = str(e).lower()
        if 'expired_access_token' in error_str or 'auth' in error_str:
            if integration.refresh_token:
                try:
                    logger.info(f"Access token expired for owner {current_owner.id}, attempting refresh for file download")
                    
                    # Refresh the token
                    token_data = dropbox_service.refresh_access_token(integration.refresh_token)
                    
                    # Update the integration with new tokens
                    integration.access_token = token_data['access_token']
                    integration.refresh_token = token_data['refresh_token']
                    db.commit()
                    
                    logger.info(f"Successfully refreshed token for owner {current_owner.id}")
                    
                    # Retry file download with new token
                    file_content = dropbox_service.download_file(
                        integration.access_token,
                        file_path
                    )
                    
                    file_metadata = dropbox_service.get_file_metadata(
                        integration.access_token,
                        file_path
                    )
                    
                    filename = file_metadata['name']
                    file_extension = filename.split('.')[-1].lower() if '.' in filename else ''
                    content_type = content_type_map.get(file_extension, content_type_map['default'])
                    
                    from fastapi.responses import Response
                    return Response(
                        content=file_content,
                        media_type=content_type,
                        headers={
                            "Content-Disposition": f"attachment; filename=\"{filename}\"",
                            "Content-Length": str(len(file_content))
                        }
                    )
                    
                except Exception as refresh_error:
                    logger.error(f"Token refresh failed for owner {current_owner.id}: {refresh_error}")
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Dropbox access token expired and refresh failed. Please reconnect your account."
                    )
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Dropbox access token expired. Please reconnect your account."
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to download file: {str(e)}"
            )


@router.get("/view-file", summary="Get file content for viewing")
async def view_file_content(
    file_path: str,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Get file content and metadata for viewing (PDF and XLS files)
    """
    integration = db.query(DropboxIntegration).filter(
        DropboxIntegration.owner_id == current_owner.id
    ).first()

    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No Dropbox integration found"
        )

    try:
        # Get file metadata first
        file_metadata = dropbox_service.get_file_metadata(
            integration.access_token,
            file_path
        )
        
        filename = file_metadata['name']
        file_extension = filename.split('.')[-1].lower() if '.' in filename else ''
        
        # Check if file type is supported for viewing
        supported_types = ['pdf', 'xls', 'xlsx', 'csv', 'txt', 'doc', 'docx']
        if file_extension not in supported_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type '{file_extension}' is not supported for viewing"
            )
        
        # For PDF files, return content directly for browser viewing
        if file_extension == 'pdf':
            file_content = dropbox_service.download_file(
                integration.access_token,
                file_path
            )
            
            from fastapi.responses import Response
            return Response(
                content=file_content,
                media_type='application/pdf',
                headers={
                    "Content-Disposition": f"inline; filename=\"{filename}\"",
                    "Content-Length": str(len(file_content))
                }
            )
        
        # For other files, return metadata and preview info
        return {
            "filename": filename,
            "file_path": file_path,
            "file_extension": file_extension,
            "size": file_metadata['size'],
            "size_formatted": format_file_size(file_metadata['size']),
            "modified": file_metadata['server_modified'],
            "can_preview": file_extension in ['pdf'],
            "can_download": True,
            "preview_url": f"/api/owner/dropbox/view-file?file_path={file_path}" if file_extension == 'pdf' else None,
            "download_url": f"/api/owner/dropbox/download-file?file_path={file_path}",
            "message": f"File '{filename}' is ready for download. Direct preview available for PDF files only."
        }

    except Exception as e:
        # Check if it's an auth error and try to refresh token
        error_str = str(e).lower()
        if 'expired_access_token' in error_str or 'auth' in error_str:
            if integration.refresh_token:
                try:
                    logger.info(f"Access token expired for owner {current_owner.id}, attempting refresh for file view")
                    
                    # Refresh the token
                    token_data = dropbox_service.refresh_access_token(integration.refresh_token)
                    
                    # Update the integration with new tokens
                    integration.access_token = token_data['access_token']
                    integration.refresh_token = token_data['refresh_token']
                    db.commit()
                    
                    logger.info(f"Successfully refreshed token for owner {current_owner.id}")
                    
                    # Retry with new token
                    file_metadata = dropbox_service.get_file_metadata(
                        integration.access_token,
                        file_path
                    )
                    
                    filename = file_metadata['name']
                    file_extension = filename.split('.')[-1].lower() if '.' in filename else ''
                    
                    if file_extension == 'pdf':
                        file_content = dropbox_service.download_file(
                            integration.access_token,
                            file_path
                        )
                        
                        from fastapi.responses import Response
                        return Response(
                            content=file_content,
                            media_type='application/pdf',
                            headers={
                                "Content-Disposition": f"inline; filename=\"{filename}\"",
                                "Content-Length": str(len(file_content))
                            }
                        )
                    
                    return {
                        "filename": filename,
                        "file_path": file_path,
                        "file_extension": file_extension,
                        "size": file_metadata['size'],
                        "size_formatted": format_file_size(file_metadata['size']),
                        "modified": file_metadata['server_modified'],
                        "can_preview": file_extension in ['pdf'],
                        "can_download": True,
                        "preview_url": f"/api/owner/dropbox/view-file?file_path={file_path}" if file_extension == 'pdf' else None,
                        "download_url": f"/api/owner/dropbox/download-file?file_path={file_path}",
                        "message": f"File '{filename}' is ready for download. Direct preview available for PDF files only."
                    }
                    
                except Exception as refresh_error:
                    logger.error(f"Token refresh failed for owner {current_owner.id}: {refresh_error}")
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Dropbox access token expired and refresh failed. Please reconnect your account."
                    )
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Dropbox access token expired. Please reconnect your account."
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to view file: {str(e)}"
            )


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


@router.get("/sync-status", response_model=DropboxSyncStatus, summary="Get sync status")
async def get_sync_status(
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Get current sync status
    """
    integration = db.query(DropboxIntegration).filter(
        DropboxIntegration.owner_id == current_owner.id
    ).first()
    
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No Dropbox integration found"
        )
    
    return DropboxSyncStatus(
        integration_id=integration.id,
        sync_status=integration.sync_status,
        last_sync_at=integration.last_sync_at,
        last_error=integration.last_error,
        files_synced=0,  # Will be implemented with actual sync
        files_pending=0,  # Will be implemented with actual sync
        sync_progress_percent=0.0  # Will be calculated during sync
    )
