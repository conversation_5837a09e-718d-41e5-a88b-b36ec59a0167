"""
Document management API endpoints for owners
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from ...core.database import get_db
from ...core.auth import get_current_owner
from ...models import Owner, Document, SyncJob
from ...schemas import (
    DocumentResponse,
    DocumentSummary,
    DocumentStats,
    DocumentContent,
    DocumentSearch,
    DocumentSearchResult
)

router = APIRouter(prefix="/documents", tags=["Document Management"])
logger = logging.getLogger(__name__)


@router.get("/", response_model=List[DocumentSummary], summary="List all documents")
async def list_documents(
    limit: int = Query(50, ge=1, le=500, description="Number of documents to return"),
    offset: int = Query(0, ge=0, description="Number of documents to skip"),
    file_type: Optional[str] = Query(None, description="Filter by file type category"),
    sync_status: Optional[str] = Query(None, description="Filter by sync status"),
    processed_only: bool = Query(False, description="Show only processed documents"),
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Get a list of all documents for the current owner with optional filtering
    """
    try:
        # Build query
        query = db.query(Document).filter(Document.owner_id == current_owner.id)
        
        # Apply filters
        if file_type:
            # Note: This would need a custom SQL function or post-filtering
            # For now, we'll filter by file extension
            pass
        
        if sync_status:
            query = query.filter(Document.sync_status == sync_status)
        
        if processed_only:
            query = query.filter(Document.is_processed == True)
        
        # Order by most recent first
        query = query.order_by(Document.updated_at.desc())
        
        # Apply pagination
        documents = query.offset(offset).limit(limit).all()
        
        # Convert to response format
        return [
            DocumentSummary(
                id=doc.id,
                filename=doc.filename,
                file_extension=doc.file_extension,
                file_size_bytes=doc.file_size_bytes,
                sync_status=doc.sync_status,
                is_processed=doc.is_processed,
                searchable=doc.searchable,
                dropbox_modified_at=doc.dropbox_modified_at,
                created_at=doc.created_at,
                file_type_category=doc.file_type_category
            )
            for doc in documents
        ]
        
    except Exception as e:
        logger.error(f"Error listing documents for owner {current_owner.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve documents"
        )


@router.get("/stats", response_model=DocumentStats, summary="Get document statistics")
async def get_document_stats(
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive statistics about the owner's documents
    """
    try:
        # Get all documents for this owner
        documents = db.query(Document).filter(Document.owner_id == current_owner.id).all()
        
        total_documents = len(documents)
        processed_documents = len([d for d in documents if d.is_processed])
        searchable_documents = len([d for d in documents if d.searchable])
        total_size_bytes = sum(d.file_size_bytes or 0 for d in documents)
        
        # File type breakdown
        file_type_breakdown = {}
        for doc in documents:
            category = doc.file_type_category
            file_type_breakdown[category] = file_type_breakdown.get(category, 0) + 1
        
        # Sync status breakdown
        sync_status_breakdown = {}
        for doc in documents:
            status = doc.sync_status
            sync_status_breakdown[status] = sync_status_breakdown.get(status, 0) + 1
        
        # Recent syncs (last 24 hours)
        from datetime import datetime, timedelta
        yesterday = datetime.utcnow() - timedelta(hours=24)
        recent_syncs = len([
            d for d in documents 
            if d.last_synced_at and d.last_synced_at > yesterday
        ])
        
        return DocumentStats(
            total_documents=total_documents,
            processed_documents=processed_documents,
            searchable_documents=searchable_documents,
            total_size_bytes=total_size_bytes,
            file_type_breakdown=file_type_breakdown,
            sync_status_breakdown=sync_status_breakdown,
            recent_syncs=recent_syncs
        )
        
    except Exception as e:
        logger.error(f"Error getting document stats for owner {current_owner.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve document statistics"
        )


@router.get("/{document_id}", response_model=DocumentResponse, summary="Get document details")
async def get_document(
    document_id: int,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific document
    """
    try:
        document = db.query(Document).filter(
            Document.id == document_id,
            Document.owner_id == current_owner.id
        ).first()
        
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        
        return document
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving document {document_id} for owner {current_owner.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve document"
        )


@router.get("/{document_id}/content", response_model=DocumentContent, summary="Get document content")
async def get_document_content(
    document_id: int,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Get the extracted text content of a document
    """
    try:
        document = db.query(Document).filter(
            Document.id == document_id,
            Document.owner_id == current_owner.id
        ).first()
        
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        
        return DocumentContent(
            document_id=document.id,
            filename=document.filename,
            extracted_text=document.extracted_text,
            text_summary=document.text_summary,
            processing_metadata=document.processing_metadata,
            can_extract_text=document.is_text_extractable
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving content for document {document_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve document content"
        )


@router.delete("/{document_id}", summary="Delete document")
async def delete_document(
    document_id: int,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Delete a document and its associated data
    """
    try:
        document = db.query(Document).filter(
            Document.id == document_id,
            Document.owner_id == current_owner.id
        ).first()
        
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        
        # Delete the document (cascade will handle related records)
        db.delete(document)
        db.commit()
        
        logger.info(f"Document {document_id} deleted by owner {current_owner.id}")
        
        return {"message": "Document deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete document"
        )
