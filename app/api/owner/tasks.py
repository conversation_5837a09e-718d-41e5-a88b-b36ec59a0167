"""
API endpoints for background task management
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import logging

from app.core.database import get_db
from app.core.auth import get_current_owner
from app.models.owner import Owner
from app.workers.celery_app import celery_app
from app.workers.tasks.test_tasks import test_basic_task, test_long_running_task, test_database_task
from app.workers.tasks.file_tasks import (
    process_dropbox_folder, 
    sync_dropbox_files, 
    generate_embeddings,
    download_dropbox_file,
    download_folder_files
)

router = APIRouter(prefix="/tasks", tags=["Tasks"])
logger = logging.getLogger(__name__)


@router.post("/test/basic", summary="Run a basic test task")
async def run_basic_test_task(
    message: str = "Hello from API!",
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Run a basic test task to verify Ce<PERSON>y is working
    """
    try:
        task = test_basic_task.delay(message)
        return {
            "task_id": task.id,
            "status": "started",
            "message": f"Basic test task started with ID: {task.id}"
        }
    except Exception as e:
        logger.error(f"Failed to start basic test task: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start task: {str(e)}"
        )


@router.post("/test/long-running", summary="Run a long-running test task")
async def run_long_running_test_task(
    duration: int = 10,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Run a long-running test task to test progress tracking
    """
    try:
        task = test_long_running_task.delay(duration)
        return {
            "task_id": task.id,
            "status": "started",
            "duration": duration,
            "message": f"Long-running test task started with ID: {task.id}"
        }
    except Exception as e:
        logger.error(f"Failed to start long-running test task: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start task: {str(e)}"
        )


@router.post("/test/database", summary="Run a database test task")
async def run_database_test_task(
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Run a database test task to verify database access from workers
    """
    try:
        task = test_database_task.delay(current_owner.id)
        return {
            "task_id": task.id,
            "status": "started",
            "owner_id": current_owner.id,
            "message": f"Database test task started with ID: {task.id}"
        }
    except Exception as e:
        logger.error(f"Failed to start database test task: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start task: {str(e)}"
        )


@router.post("/dropbox/process-folder", summary="Process Dropbox folder")
async def process_folder(
    folder_path: str,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Start processing files in a Dropbox folder
    """
    try:
        task = process_dropbox_folder.delay(current_owner.id, folder_path)
        return {
            "task_id": task.id,
            "status": "started",
            "folder_path": folder_path,
            "message": f"Folder processing task started with ID: {task.id}"
        }
    except Exception as e:
        logger.error(f"Failed to start folder processing task: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start task: {str(e)}"
        )


@router.post("/dropbox/sync", summary="Sync Dropbox files")
async def sync_dropbox(
    incremental: bool = False,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Start syncing files from Dropbox
    """
    try:
        task = sync_dropbox_files.delay(current_owner.id, incremental)
        return {
            "task_id": task.id,
            "status": "started",
            "incremental": incremental,
            "message": f"Dropbox sync task started with ID: {task.id}"
        }
    except Exception as e:
        logger.error(f"Failed to start sync task: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start task: {str(e)}"
        )


@router.post("/download/file", summary="Download single file from Dropbox")
async def download_single_file(
    file_path: str,
    force_redownload: bool = False,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Download a single file from Dropbox and store it locally
    """
    try:
        task = download_dropbox_file.delay(
            current_owner.id, 
            file_path, 
            force_redownload
        )
        return {
            "task_id": task.id,
            "status": "started",
            "file_path": file_path,
            "force_redownload": force_redownload,
            "message": f"File download task started with ID: {task.id}"
        }
    except Exception as e:
        logger.error(f"Failed to start file download task: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start task: {str(e)}"
        )


@router.post("/download/folders", summary="Download files from multiple folders")
async def download_folder_files_endpoint(
    folder_paths: List[str],
    file_types: List[str] = None,
    max_files: int = None,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Download files from specified Dropbox folders
    
    Args:
        folder_paths: List of folder paths to process
        file_types: Optional file extensions to filter (e.g., ["pdf", "docx"])
        max_files: Maximum number of files to download
    """
    try:
        task = download_folder_files.delay(
            current_owner.id,
            folder_paths,
            file_types,
            max_files
        )
        return {
            "task_id": task.id,
            "status": "started",
            "folder_paths": folder_paths,
            "file_types": file_types,
            "max_files": max_files,
            "message": f"Folder download task started with ID: {task.id}"
        }
    except Exception as e:
        logger.error(f"Failed to start folder download task: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start task: {str(e)}"
        )


@router.get("/status/{task_id}", summary="Get task status")
async def get_task_status(
    task_id: str,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Get the status and progress of a background task
    """
    try:
        task = celery_app.AsyncResult(task_id)
        
        if task.state == 'PENDING':
            response = {
                'task_id': task_id,
                'state': task.state,
                'status': 'Task is waiting to be processed...'
            }
        elif task.state == 'PROGRESS':
            response = {
                'task_id': task_id,
                'state': task.state,
                'current': task.info.get('current', 0),
                'total': task.info.get('total', 1),
                'status': task.info.get('status', 'In progress...')
            }
        elif task.state == 'SUCCESS':
            response = {
                'task_id': task_id,
                'state': task.state,
                'result': task.result
            }
        elif task.state == 'FAILURE':
            response = {
                'task_id': task_id,
                'state': task.state,
                'error': str(task.info)
            }
        else:
            response = {
                'task_id': task_id,
                'state': task.state,
                'status': 'Unknown task state'
            }
        
        return response
        
    except Exception as e:
        logger.error(f"Failed to get task status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get task status: {str(e)}"
        )


@router.get("/list", summary="List recent tasks")
async def list_tasks(
    limit: int = 20,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    List recent tasks (basic implementation)
    """
    try:
        # Get active and reserved tasks
        inspect = celery_app.control.inspect()
        active_tasks = inspect.active()
        reserved_tasks = inspect.reserved()
        
        tasks = []
        
        # Add active tasks
        if active_tasks:
            for worker, task_list in active_tasks.items():
                for task in task_list:
                    tasks.append({
                        'task_id': task['id'],
                        'name': task['name'],
                        'worker': worker,
                        'state': 'ACTIVE'
                    })
        
        # Add reserved tasks
        if reserved_tasks:
            for worker, task_list in reserved_tasks.items():
                for task in task_list:
                    tasks.append({
                        'task_id': task['id'],
                        'name': task['name'],
                        'worker': worker,
                        'state': 'RESERVED'
                    })
        
        return {
            'tasks': tasks[:limit],
            'total': len(tasks)
        }
        
    except Exception as e:
        logger.error(f"Failed to list tasks: {str(e)}")
        return {
            'tasks': [],
            'total': 0,
            'error': f"Failed to retrieve tasks: {str(e)}"
        }


@router.delete("/cancel/{task_id}", summary="Cancel a task")
async def cancel_task(
    task_id: str,
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
):
    """
    Cancel a running task
    """
    try:
        celery_app.control.revoke(task_id, terminate=True)
        return {
            'task_id': task_id,
            'status': 'cancelled',
            'message': f'Task {task_id} has been cancelled'
        }
    except Exception as e:
        logger.error(f"Failed to cancel task: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel task: {str(e)}"
        )
