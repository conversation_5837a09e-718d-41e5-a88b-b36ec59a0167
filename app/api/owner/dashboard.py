"""
Owner dashboard API endpoints
"""
from fastapi import APIR<PERSON>er, Depends, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from typing import Dict, Any
import logging

from ...core.database import get_db
from ...core.auth import get_current_owner, get_current_owner_optional, get_current_active_owner
from ...models import Owner, DropboxIntegration, Document
from ...services.dropbox_service import DropboxService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/dashboard", tags=["Owner Dashboard"])

# Templates setup
templates = Jinja2Templates(directory="app/templates")


@router.get("/", response_class=HTMLResponse, summary="Owner dashboard home page")
async def dashboard_home(request: Request):
    """
    Serve the main dashboard page - authentication handled by client-side JavaScript
    """
    # Always serve dashboard template, let client-side handle authentication
    # We'll rely on JavaScript for dynamic loading since server-side auth is complex here
    dashboard_data = {
        "request": request,
        "title": "CA Firm Dashboard"
    }
    
    return templates.TemplateResponse("dashboard.html", dashboard_data)


@router.get("/login", response_class=HTMLResponse, summary="Login page")
async def login_page(request: Request):
    """
    Serve the login page
    """
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "title": "CA Firm Login"}
    )


@router.get("/register", response_class=HTMLResponse, summary="Registration page")
async def register_page(request: Request):
    """
    Serve the registration page
    """
    return templates.TemplateResponse(
        "register.html",
        {"request": request, "title": "Register CA Firm"}
    )


@router.get("/api/stats", summary="Get dashboard statistics")
async def get_dashboard_stats(
    current_owner: Owner = Depends(get_current_owner),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get dashboard statistics for the current owner with real data
    """
    try:
        # Initialize dropbox service
        dropbox_service = DropboxService()
        
        # Get Dropbox integration
        integration = db.query(DropboxIntegration).filter(
            DropboxIntegration.owner_id == current_owner.id
        ).first()
        
        # Initialize counts
        total_files = 0
        processed_files = 0
        syncing_files = 0
        searchable_files = 0
        dropbox_connected = False
        last_sync = None
        
        if integration:
            dropbox_connected = True
            try:
                # Get total files from Dropbox CA_Firm folder
                ca_firm_files = dropbox_service.list_files_recursive(
                    integration.access_token, 
                    "/CA_Firm",
                    file_types=['pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'txt']
                )
                total_files = len(ca_firm_files)
                
                # Get last sync time from integration
                if integration.updated_at:
                    last_sync = integration.updated_at.strftime("%Y-%m-%d %H:%M:%S")
                    
            except Exception as e:
                logger.warning(f"Failed to get Dropbox stats: {str(e)}")
                # Try to refresh token if needed
                try:
                    new_token = dropbox_service.refresh_access_token(integration.refresh_token)
                    integration.access_token = new_token
                    db.commit()
                    
                    # Retry getting files
                    ca_firm_files = dropbox_service.list_files_recursive(
                        integration.access_token,
                        "/CA_Firm", 
                        file_types=['pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'txt']
                    )
                    total_files = len(ca_firm_files)
                except Exception as refresh_error:
                    logger.error(f"Failed to refresh token and get stats: {str(refresh_error)}")
        
        # Get processed files from database
        documents = db.query(Document).filter(Document.owner_id == current_owner.id).all()
        
        for doc in documents:
            if doc.sync_status == "completed":
                processed_files += 1
                if doc.searchable:
                    searchable_files += 1
            elif doc.sync_status == "syncing" or doc.sync_status == "processing":
                syncing_files += 1
        
        stats = {
            "owner_info": {
                "id": current_owner.id,
                "name": current_owner.full_name,
                "firm": current_owner.firm_name,
                "email": current_owner.email,
                "member_since": current_owner.created_at.strftime("%B %Y")
            },
            "system_stats": {
                "total_owners": db.query(Owner).count(),
                "documents_count": total_files,       # Real count from Dropbox
                "processed_count": processed_files,    # Files downloaded and processed
                "syncing_count": syncing_files,        # Files currently syncing
                "searchable_count": searchable_files,  # Files with embeddings
                "clients_count": 0,                    # Placeholder for future
                "queries_this_month": 0,               # Placeholder for future
                "dropbox_connected": dropbox_connected,
                "last_sync": last_sync
            },
            "recent_activity": [
                {
                    "action": f"Found {total_files} files in CA_Firm folder",
                    "timestamp": last_sync or "Never",
                    "description": f"Dropbox sync status: {'Connected' if dropbox_connected else 'Disconnected'}"
                },
                {
                    "action": f"{processed_files} files processed",
                    "timestamp": last_sync or "Never", 
                    "description": f"{searchable_files} files ready for AI search"
                }
            ]
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting dashboard stats: {str(e)}")
        # Return safe defaults on error
        return {
            "owner_info": {
                "id": current_owner.id,
                "name": current_owner.full_name,
                "firm": current_owner.firm_name,
                "email": current_owner.email,
                "member_since": current_owner.created_at.strftime("%B %Y")
            },
            "system_stats": {
                "total_owners": db.query(Owner).count(),
                "documents_count": 0,
                "processed_count": 0,
                "syncing_count": 0,
                "searchable_count": 0,
                "clients_count": 0,
                "queries_this_month": 0,
                "dropbox_connected": False,
                "last_sync": None
            },
            "recent_activity": [
                {
                    "action": "Error loading statistics",
                    "timestamp": "Just now",
                    "description": "Please check your Dropbox connection"
                }
            ]
        }


@router.get("/profile", response_class=HTMLResponse, summary="Owner profile page")
async def profile_page(
    request: Request,
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Serve the owner profile management page
    """
    return templates.TemplateResponse(
        "profile.html",
        {
            "request": request,
            "title": "Owner Profile",
            "owner": current_owner
        }
    )
