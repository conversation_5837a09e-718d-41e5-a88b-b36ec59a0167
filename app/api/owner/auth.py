"""
Owner authentication API endpoints
"""
from datetime import <PERSON><PERSON><PERSON>
from fastapi import <PERSON><PERSON>outer, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>Re<PERSON>Form
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...core.security import verify_password, get_password_hash, create_access_token
from ...core.auth import get_current_active_owner
from ...models import Owner
from ...schemas import (
    OwnerCreate, 
    OwnerResponse, 
    OwnerLogin, 
    OwnerLoginResponse
)

router = APIRouter(prefix="/auth", tags=["Owner Authentication"])


@router.post("/register", response_model=OwnerResponse, summary="Register new CA firm owner")
async def register_owner(
    owner_data: OwnerCreate,
    db: Session = Depends(get_db)
):
    """
    Register a new CA firm owner account
    """
    # Check if owner already exists
    existing_owner = db.query(Owner).filter(Owner.email == owner_data.email).first()
    if existing_owner:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Hash the password
    hashed_password = get_password_hash(owner_data.password)
    
    # Create new owner
    db_owner = Owner(
        email=owner_data.email,
        full_name=owner_data.full_name,
        firm_name=owner_data.firm_name,
        phone=owner_data.phone,
        address=owner_data.address,
        timezone=owner_data.timezone,
        hashed_password=hashed_password,
        is_active=True,
        is_verified=False  # Email verification can be added later
    )
    
    try:
        db.add(db_owner)
        db.commit()
        db.refresh(db_owner)
        return db_owner
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create owner: {str(e)}"
        )


@router.post("/login", response_model=OwnerLoginResponse, summary="Owner login")
async def login_owner(
    login_data: OwnerLogin,
    db: Session = Depends(get_db)
):
    """
    Authenticate owner and return JWT access token
    """
    # Find owner by email
    owner = db.query(Owner).filter(Owner.email == login_data.email).first()
    
    if not owner:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Verify password
    if not verify_password(login_data.password, owner.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Check if owner is active
    if not owner.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive account"
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=30)  # Can be configured
    access_token = create_access_token(
        data={"sub": owner.email},
        expires_delta=access_token_expires
    )
    
    return OwnerLoginResponse(
        access_token=access_token,
        token_type="bearer",
        owner=OwnerResponse.from_orm(owner)
    )


@router.post("/token", summary="OAuth2 compatible token endpoint")
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    OAuth2 compatible token endpoint (for Swagger UI)
    """
    # Find owner by email (username in OAuth2 form)
    owner = db.query(Owner).filter(Owner.email == form_data.username).first()
    
    if not owner or not verify_password(form_data.password, owner.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not owner.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive account"
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": owner.email},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }


@router.get("/me", response_model=OwnerResponse, summary="Get current owner profile")
async def get_current_owner_profile(
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get the profile information of the currently authenticated owner
    """
    return current_owner


@router.get("/verify-token", summary="Verify JWT token")
async def verify_current_token(
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Verify that the current JWT token is valid
    """
    return {
        "valid": True,
        "owner_email": current_owner.email,
        "owner_name": current_owner.full_name,
        "firm_name": current_owner.firm_name
    }
