"""
Client management API endpoints for CA firm owners
"""
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_

from ...core.database import get_db
from ...core.auth import get_current_active_owner
from ...core.security import get_password_hash
from ...models import Owner, <PERSON><PERSON>, ClientApiKey, Document
from ...schemas import (
    ClientCreate,
    ClientUpdate,
    ClientResponse,
    ClientSummary,
    ClientSearchRequest,
    ClientSearchResponse,
    BulkClientCreate,
    BulkClientResponse,
    ClientFolderAccessUpdate,
    ClientUsageStats,
    ClientApiKeyCreate,
    ClientApiKeyResponse,
    ClientApiKeyListResponse
)

router = APIRouter(prefix="/clients", tags=["Client Management"])


@router.post("/", response_model=ClientResponse, summary="Create new client")
async def create_client(
    client_data: ClientCreate,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Create a new client for the CA firm
    """
    # Check if client email already exists
    existing_client = db.query(Client).filter(Client.email == client_data.email).first()
    if existing_client:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Client with this email already exists"
        )
    
    # Hash the password
    hashed_password = get_password_hash(client_data.password)
    
    # Create new client
    db_client = Client(
        owner_id=current_owner.id,
        email=client_data.email,
        full_name=client_data.full_name,
        company_name=client_data.company_name,
        phone=client_data.phone,
        
        # Address information
        address_line1=client_data.address_line1,
        address_line2=client_data.address_line2,
        city=client_data.city,
        state=client_data.state,
        postal_code=client_data.postal_code,
        country=client_data.country,
        
        # Authentication
        hashed_password=hashed_password,
        has_web_access=client_data.has_web_access,
        has_api_access=client_data.has_api_access,
        
        # Document access settings
        assigned_folder_paths=client_data.assigned_folder_paths,
        allowed_document_types=client_data.allowed_document_types,
        allowed_file_extensions=client_data.allowed_file_extensions,
        
        # Business relationship details
        client_type=client_data.client_type,
        industry=client_data.industry,
        annual_revenue=client_data.annual_revenue,
        tax_id=client_data.tax_id,
        registration_number=client_data.registration_number,
        
        # Service details
        services_subscribed=client_data.services_subscribed,
        retainer_amount=client_data.retainer_amount,
        billing_frequency=client_data.billing_frequency,
        
        # Notification settings
        email_notifications=client_data.email_notifications,
        sms_notifications=client_data.sms_notifications,
        notification_frequency=client_data.notification_frequency,
        
        # Security settings
        two_factor_enabled=client_data.two_factor_enabled,
        session_timeout_minutes=client_data.session_timeout_minutes,
        
        # AI Bot settings
        bot_access_enabled=client_data.bot_access_enabled,
        daily_question_limit=client_data.daily_question_limit,
        search_scope=client_data.search_scope,
        include_archived_documents=client_data.include_archived_documents,
        search_history_retention_days=client_data.search_history_retention_days,
        
        # Default values
        is_active=True,
        is_verified=False,
        total_questions_asked=0
    )
    
    try:
        db.add(db_client)
        db.commit()
        db.refresh(db_client)
        return db_client
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create client: {str(e)}"
        )


@router.get("/", response_model=List[ClientSummary], summary="List all clients")
async def list_clients(
    skip: int = Query(0, ge=0, description="Number of clients to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of clients to return"),
    search: Optional[str] = Query(None, description="Search by name or email"),
    client_type: Optional[str] = Query(None, description="Filter by client type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get list of all clients for the current owner with optional filtering
    """
    query = db.query(Client).filter(Client.owner_id == current_owner.id)
    
    # Apply filters
    if search:
        search_filter = or_(
            Client.full_name.ilike(f"%{search}%"),
            Client.email.ilike(f"%{search}%"),
            Client.company_name.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    if client_type:
        query = query.filter(Client.client_type == client_type)
    
    if is_active is not None:
        query = query.filter(Client.is_active == is_active)
    
    # Order by creation date (newest first)
    query = query.order_by(Client.created_at.desc())
    
    # Apply pagination
    clients = query.offset(skip).limit(limit).all()
    
    return clients


@router.get("/{client_id}", response_model=ClientResponse, summary="Get client by ID")
async def get_client(
    client_id: int,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get a specific client by ID
    """
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.owner_id == current_owner.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    return client


@router.put("/{client_id}", response_model=ClientResponse, summary="Update client")
async def update_client(
    client_id: int,
    client_data: ClientUpdate,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Update client information
    """
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.owner_id == current_owner.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Update only provided fields
    update_data = client_data.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(client, field, value)
    
    try:
        db.commit()
        db.refresh(client)
        return client
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update client: {str(e)}"
        )


@router.delete("/{client_id}", summary="Delete client")
async def delete_client(
    client_id: int,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Delete a client and all associated data
    """
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.owner_id == current_owner.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    try:
        # Delete client (cascade will handle related records)
        db.delete(client)
        db.commit()
        
        return {"message": f"Client {client.full_name} has been deleted successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete client: {str(e)}"
        )


@router.post("/search", response_model=ClientSearchResponse, summary="Advanced client search")
async def search_clients(
    search_request: ClientSearchRequest,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Advanced client search with filtering and pagination
    """
    query = db.query(Client).filter(Client.owner_id == current_owner.id)
    
    # Apply search filters
    if search_request.search_term:
        search_filter = or_(
            Client.full_name.ilike(f"%{search_request.search_term}%"),
            Client.email.ilike(f"%{search_request.search_term}%"),
            Client.company_name.ilike(f"%{search_request.search_term}%")
        )
        query = query.filter(search_filter)
    
    if search_request.client_type:
        query = query.filter(Client.client_type == search_request.client_type)
    
    if search_request.is_active is not None:
        query = query.filter(Client.is_active == search_request.is_active)
    
    if search_request.has_web_access is not None:
        query = query.filter(Client.has_web_access == search_request.has_web_access)
    
    if search_request.has_api_access is not None:
        query = query.filter(Client.has_api_access == search_request.has_api_access)
    
    if search_request.created_after:
        query = query.filter(Client.created_at >= search_request.created_after)
    
    if search_request.created_before:
        query = query.filter(Client.created_at <= search_request.created_before)
    
    if search_request.last_login_after:
        query = query.filter(Client.last_login_at >= search_request.last_login_after)
    
    # Apply sorting
    sort_column = getattr(Client, search_request.sort_by)
    if search_request.sort_order == "desc":
        query = query.order_by(sort_column.desc())
    else:
        query = query.order_by(sort_column.asc())
    
    # Get total count
    total_count = query.count()
    
    # Apply pagination
    offset = (search_request.page - 1) * search_request.page_size
    clients = query.offset(offset).limit(search_request.page_size).all()
    
    # Calculate pagination info
    total_pages = (total_count + search_request.page_size - 1) // search_request.page_size
    has_next = search_request.page < total_pages
    has_previous = search_request.page > 1
    
    return ClientSearchResponse(
        clients=clients,
        total_count=total_count,
        page=search_request.page,
        page_size=search_request.page_size,
        total_pages=total_pages,
        has_next=has_next,
        has_previous=has_previous
    )


@router.patch("/{client_id}/toggle-status", response_model=ClientResponse, summary="Toggle client active status")
async def toggle_client_status(
    client_id: int,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Toggle client active/inactive status
    """
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.owner_id == current_owner.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    client.is_active = not client.is_active
    
    try:
        db.commit()
        db.refresh(client)
        return client
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update client status: {str(e)}"
        )


@router.post("/{client_id}/folder-access", summary="Update client folder access")
async def update_client_folder_access(
    client_id: int,
    folder_paths: List[str],
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Update folder access permissions for a client
    """
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.owner_id == current_owner.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Validate folder paths exist in owner's documents
    if folder_paths:
        for path in folder_paths:
            # Check if owner has documents in this path
            doc_exists = db.query(Document).filter(
                and_(
                    Document.owner_id == current_owner.id,
                    Document.dropbox_path.like(f"{path}%")
                )
            ).first()
            
            if not doc_exists:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"No documents found in path: {path}"
                )
    
    client.assigned_folder_paths = folder_paths
    
    try:
        db.commit()
        return {"message": f"Folder access updated for {client.full_name}"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update folder access: {str(e)}"
        )


@router.get("/{client_id}/usage-stats", response_model=ClientUsageStats, summary="Get client usage statistics")
async def get_client_usage_stats(
    client_id: int,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get usage statistics for a specific client
    """
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.owner_id == current_owner.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Calculate usage statistics
    # Note: This would be more sophisticated in a real implementation
    # with proper date-based queries for monthly/daily stats
    
    return ClientUsageStats(
        client_id=client.id,
        total_questions=client.total_questions_asked,
        questions_this_month=0,  # TODO: Implement monthly count
        questions_today=0,  # TODO: Implement daily count
        average_confidence_score=None,  # TODO: Calculate from chat sessions
        most_used_documents=[],  # TODO: Implement from chat history
        last_active_date=client.last_question_at,
        session_count=0  # TODO: Count unique sessions
    )


@router.post("/bulk", response_model=BulkClientResponse, summary="Create multiple clients")
async def create_bulk_clients(
    bulk_data: BulkClientCreate,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Create multiple clients in a single request
    """
    created_clients = []
    failed_clients = []
    
    for client_data in bulk_data.clients:
        try:
            # Check if client email already exists
            existing_client = db.query(Client).filter(Client.email == client_data.email).first()
            if existing_client:
                failed_clients.append({
                    "email": client_data.email,
                    "error": "Email already exists"
                })
                continue
            
            # Hash the password
            hashed_password = get_password_hash(client_data.password)
            
            # Apply default folder access if provided and client doesn't have specific paths
            folder_paths = client_data.assigned_folder_paths or bulk_data.default_folder_access
            
            # Create new client
            db_client = Client(
                owner_id=current_owner.id,
                email=client_data.email,
                full_name=client_data.full_name,
                company_name=client_data.company_name,
                hashed_password=hashed_password,
                assigned_folder_paths=folder_paths,
                # ... (other fields similar to create_client)
                is_active=True,
                is_verified=False
            )
            
            db.add(db_client)
            db.commit()
            db.refresh(db_client)
            
            created_clients.append(db_client)
            
        except Exception as e:
            db.rollback()
            failed_clients.append({
                "email": client_data.email,
                "error": str(e)
            })
    
    return BulkClientResponse(
        created=created_clients,
        failed=failed_clients,
        total_attempted=len(bulk_data.clients),
        total_created=len(created_clients),
        total_failed=len(failed_clients)
    )


# Client API Key Management endpoints

@router.get("/{client_id}/api-keys", response_model=List[ClientApiKeyListResponse], summary="List client API keys")
async def list_client_api_keys(
    client_id: int,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    List all API keys for a specific client
    """
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.owner_id == current_owner.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    api_keys = db.query(ClientApiKey).filter(ClientApiKey.client_id == client_id).all()
    
    # Mask API keys for security
    masked_keys = []
    for key in api_keys:
        masked_key = ClientApiKeyListResponse(
            id=key.id,
            name=key.name,
            description=key.description,
            api_key_preview=f"{key.api_key[:8]}...{key.api_key[-4:]}",
            is_active=key.is_active,
            rate_limit_per_hour=key.rate_limit_per_hour,
            last_used_at=key.last_used_at,
            total_requests=key.total_requests,
            expires_at=key.expires_at,
            created_at=key.created_at
        )
        masked_keys.append(masked_key)
    
    return masked_keys


@router.post("/{client_id}/api-keys", response_model=ClientApiKeyResponse, summary="Create client API key")
async def create_client_api_key(
    client_id: int,
    api_key_data: ClientApiKeyCreate,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Create a new API key for a client
    """
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.owner_id == current_owner.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    if not client.has_api_access:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Client does not have API access enabled"
        )
    
    # Generate secure API key
    import secrets
    api_key = f"ck_{secrets.token_urlsafe(32)}"
    
    # Create API key record
    db_api_key = ClientApiKey(
        client_id=client_id,
        api_key=api_key,
        name=api_key_data.name,
        description=api_key_data.description,
        rate_limit_per_hour=api_key_data.rate_limit_per_hour,
        allowed_endpoints=api_key_data.allowed_endpoints,
        expires_at=api_key_data.expires_at,
        created_by_owner_id=current_owner.id,
        is_active=True,
        total_requests=0
    )
    
    try:
        db.add(db_api_key)
        db.commit()
        db.refresh(db_api_key)
        return db_api_key
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create API key: {str(e)}"
        )

@router.get("/folder-tree/{owner_id}", summary="Get folder tree for owner's documents")
async def get_folder_tree(
    owner_id: int,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get hierarchical folder tree of all documents belonging to the owner.
    This is used for assigning folder access to clients.
    """
    if current_owner.id != owner_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only access your own folder tree"
        )
    
    try:
        folder_tree = folder_access_service.get_owner_folder_tree(owner_id, db)
        return folder_tree
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load folder tree: {str(e)}"
        )
