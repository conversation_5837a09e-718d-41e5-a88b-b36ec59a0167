"""
Embedding management API endpoints for CA firm owners
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...core.auth import get_current_active_owner
from ...models import Owner, Document, DocumentEmbedding, Client
from ...services.enhanced_embedding_service import EnhancedEmbeddingService, EmbeddingModelConfig
from ...schemas import DocumentResponse

router = APIRouter(prefix="/embeddings", tags=["Embedding Management"])

# Initialize embedding service
embedding_service = EnhancedEmbeddingService()


@router.get("/models", summary="List available embedding models")
async def list_embedding_models():
    """
    Get list of available embedding models and their configurations
    """
    return {
        "available_models": EmbeddingModelConfig.list_available_models(),
        "model_details": EmbeddingModelConfig.MODELS,
        "current_model": {
            "provider": embedding_service.provider,
            "model_name": embedding_service.model_name,
            "dimension": embedding_service.vector_dimension,
            "max_tokens": embedding_service.max_tokens
        }
    }


@router.get("/stats", summary="Get embedding statistics")
async def get_embedding_statistics(
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get comprehensive embedding statistics for the current owner
    """
    stats = embedding_service.get_embedding_statistics(db, current_owner.id)
    
    # Add owner-specific document stats
    total_docs = db.query(Document).filter(Document.owner_id == current_owner.id).count()
    docs_with_embeddings = db.query(Document).filter(
        Document.owner_id == current_owner.id,
        Document.embeddings_generated == True
    ).count()
    
    stats.update({
        "owner_total_documents": total_docs,
        "owner_documents_with_embeddings": docs_with_embeddings,
        "embedding_coverage_percentage": round((docs_with_embeddings / total_docs * 100) if total_docs > 0 else 0, 2)
    })
    
    return stats


@router.post("/process-document/{document_id}", summary="Generate embeddings for specific document")
async def process_document_embeddings(
    document_id: int,
    background_tasks: BackgroundTasks,
    force_regenerate: bool = Query(False, description="Force regeneration of existing embeddings"),
    chunk_size: Optional[int] = Query(None, ge=100, le=2000, description="Custom chunk size"),
    chunk_overlap: Optional[int] = Query(None, ge=0, le=500, description="Custom chunk overlap"),
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Generate embeddings for a specific document
    """
    # Verify document belongs to current owner
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.owner_id == current_owner.id
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    if not document.extracted_text:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Document has no extracted text. Please process the document for text extraction first."
        )
    
    # Check if embeddings already exist
    existing_embeddings = db.query(DocumentEmbedding).filter(
        DocumentEmbedding.document_id == document_id
    ).count()
    
    if existing_embeddings > 0 and not force_regenerate:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Embeddings already exist for this document. Use force_regenerate=true to regenerate."
        )
    
    # Process embeddings in background
    background_tasks.add_task(
        _process_document_embeddings_task,
        document_id,
        db,
        force_regenerate,
        chunk_size,
        chunk_overlap
    )
    
    return {
        "message": f"Embedding generation started for document {document.filename}",
        "document_id": document_id,
        "document_name": document.filename,
        "force_regenerate": force_regenerate,
        "estimated_chunks": len(document.extracted_text) // (chunk_size or 1000)
    }


@router.post("/process-batch", summary="Generate embeddings for multiple documents")
async def process_batch_embeddings(
    document_ids: List[int],
    background_tasks: BackgroundTasks,
    force_regenerate: bool = Query(False, description="Force regeneration of existing embeddings"),
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Generate embeddings for multiple documents in batch
    """
    # Verify all documents belong to current owner
    documents = db.query(Document).filter(
        Document.id.in_(document_ids),
        Document.owner_id == current_owner.id
    ).all()
    
    if len(documents) != len(document_ids):
        found_ids = [doc.id for doc in documents]
        missing_ids = set(document_ids) - set(found_ids)
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Documents not found: {list(missing_ids)}"
        )
    
    # Filter documents that have extracted text
    processable_docs = [doc for doc in documents if doc.extracted_text]
    skipped_docs = [doc for doc in documents if not doc.extracted_text]
    
    # Start background processing
    background_tasks.add_task(
        _process_batch_embeddings_task,
        [doc.id for doc in processable_docs],
        db,
        force_regenerate
    )
    
    return {
        "message": f"Batch embedding generation started for {len(processable_docs)} documents",
        "processable_documents": len(processable_docs),
        "skipped_documents": len(skipped_docs),
        "skipped_document_names": [doc.filename for doc in skipped_docs],
        "force_regenerate": force_regenerate
    }


@router.post("/process-all", summary="Generate embeddings for all owner documents")
async def process_all_embeddings(
    background_tasks: BackgroundTasks,
    force_regenerate: bool = Query(False, description="Force regeneration of existing embeddings"),
    only_missing: bool = Query(True, description="Only process documents without embeddings"),
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Generate embeddings for all documents belonging to the current owner
    """
    # Get all documents with extracted text
    query = db.query(Document).filter(
        Document.owner_id == current_owner.id,
        Document.extracted_text.isnot(None),
        Document.extracted_text != ""
    )
    
    if only_missing and not force_regenerate:
        # Only process documents without embeddings
        query = query.filter(Document.embeddings_generated == False)
    
    documents = query.all()
    
    if not documents:
        return {
            "message": "No documents found for processing",
            "total_documents": 0
        }
    
    # Start background processing
    background_tasks.add_task(
        _process_batch_embeddings_task,
        [doc.id for doc in documents],
        db,
        force_regenerate
    )
    
    return {
        "message": f"Embedding generation started for {len(documents)} documents",
        "total_documents": len(documents),
        "force_regenerate": force_regenerate,
        "only_missing": only_missing
    }


@router.get("/document/{document_id}", summary="Get embeddings for specific document")
async def get_document_embeddings(
    document_id: int,
    include_vectors: bool = Query(False, description="Include embedding vectors in response"),
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get all embeddings for a specific document
    """
    # Verify document belongs to current owner
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.owner_id == current_owner.id
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Get embeddings
    embeddings = db.query(DocumentEmbedding).filter(
        DocumentEmbedding.document_id == document_id
    ).order_by(DocumentEmbedding.chunk_index).all()
    
    # Format response
    result = {
        "document_id": document_id,
        "document_name": document.filename,
        "total_embeddings": len(embeddings),
        "embeddings": []
    }
    
    for embedding in embeddings:
        embedding_data = {
            "id": embedding.id,
            "chunk_index": embedding.chunk_index,
            "text_chunk": embedding.text_chunk,
            "text_preview": embedding.text_chunk[:200] + "..." if len(embedding.text_chunk) > 200 else embedding.text_chunk,
            "chunk_start_char": embedding.chunk_start_char,
            "chunk_end_char": embedding.chunk_end_char,
            "embedding_model": embedding.embedding_model,
            "vector_dimension": embedding.vector_dimension,
            "processing_status": embedding.processing_status,
            "processed_at": embedding.processed_at,
            "is_searchable": embedding.is_searchable,
            "relevance_score": embedding.relevance_score
        }
        
        if include_vectors:
            embedding_data["embedding_vector"] = embedding.get_embedding_vector()
        
        result["embeddings"].append(embedding_data)
    
    return result


@router.delete("/document/{document_id}", summary="Delete embeddings for specific document")
async def delete_document_embeddings(
    document_id: int,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Delete all embeddings for a specific document
    """
    # Verify document belongs to current owner
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.owner_id == current_owner.id
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Delete embeddings
    deleted_count = db.query(DocumentEmbedding).filter(
        DocumentEmbedding.document_id == document_id
    ).delete()
    
    # Update document status
    document.embeddings_generated = False
    document.searchable = False
    
    try:
        db.commit()
        return {
            "message": f"Deleted {deleted_count} embeddings for document {document.filename}",
            "document_id": document_id,
            "embeddings_deleted": deleted_count
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete embeddings: {str(e)}"
        )


@router.post("/search", summary="Search documents using semantic similarity")
async def search_documents_by_similarity(
    query: str,
    client_id: Optional[int] = Query(None, description="Search within specific client's accessible documents"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results"),
    similarity_threshold: float = Query(0.6, ge=0.0, le=1.0, description="Minimum similarity score"),
    document_ids: Optional[List[int]] = Query(None, description="Search within specific documents"),
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Search documents using semantic similarity
    """
    if not query or not query.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Query text is required"
        )
    
    # If client_id is provided, verify client belongs to owner
    client = None
    if client_id:
        client = db.query(Client).filter(
            Client.id == client_id,
            Client.owner_id == current_owner.id
        ).first()
        
        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Client not found"
            )
    
    # If no client specified, create a dummy client with full access for the owner
    if not client:
        # Create temporary client object for searching all owner documents
        from ...models import Client
        temp_client = Client()
        temp_client.owner_id = current_owner.id
        temp_client.assigned_folder_paths = ["/"]  # Access to all folders
        client = temp_client
    
    # Perform similarity search
    results = embedding_service.search_similar_chunks(
        query_text=query.strip(),
        client=client,
        db=db,
        limit=limit,
        similarity_threshold=similarity_threshold,
        document_ids=document_ids
    )
    
    return {
        "query": query,
        "total_results": len(results),
        "similarity_threshold": similarity_threshold,
        "client_id": client_id,
        "results": results
    }


@router.post("/cleanup", summary="Clean up orphaned and failed embeddings")
async def cleanup_embeddings(
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Clean up orphaned and failed embeddings for the current owner
    """
    cleanup_stats = embedding_service.cleanup_embeddings(db, current_owner.id)
    
    return {
        "message": "Embedding cleanup completed",
        "owner_id": current_owner.id,
        "cleanup_statistics": cleanup_stats
    }


# Background task functions

async def _process_document_embeddings_task(
    document_id: int,
    db: Session,
    force_regenerate: bool,
    chunk_size: Optional[int],
    chunk_overlap: Optional[int]
):
    """Background task to process document embeddings"""
    try:
        result = embedding_service.process_document_for_embeddings(
            document_id=document_id,
            db=db,
            force_regenerate=force_regenerate,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        
        logger.info(f"Document {document_id} embedding processing completed: {result}")
        
    except Exception as e:
        logger.error(f"Background embedding processing failed for document {document_id}: {e}")


async def _process_batch_embeddings_task(
    document_ids: List[int],
    db: Session,
    force_regenerate: bool
):
    """Background task to process multiple document embeddings"""
    try:
        total_processed = 0
        total_failed = 0
        
        for doc_id in document_ids:
            try:
                result = embedding_service.process_document_for_embeddings(
                    document_id=doc_id,
                    db=db,
                    force_regenerate=force_regenerate
                )
                
                if result["success"]:
                    total_processed += 1
                else:
                    total_failed += 1
                    
            except Exception as e:
                logger.error(f"Failed to process embeddings for document {doc_id}: {e}")
                total_failed += 1
        
        logger.info(f"Batch embedding processing completed: {total_processed} successful, {total_failed} failed")
        
    except Exception as e:
        logger.error(f"Batch embedding processing failed: {e}")


# Import logger
import logging
logger = logging.getLogger(__name__)
