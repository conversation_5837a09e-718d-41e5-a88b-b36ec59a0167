"""
Client document access management API endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...core.auth import get_current_active_owner
from ...models import Owner, Client, Document
from ...services.folder_access_service import FolderAccessService
from ...schemas import (
    DocumentResponse,
    DocumentSummary,
    ClientFolderAccessUpdate
)

router = APIRouter(prefix="/clients", tags=["Client Document Access"])

# Initialize folder access service
folder_access_service = FolderAccessService()


@router.get("/{client_id}/documents", response_model=List[DocumentSummary], summary="Get client accessible documents")
async def get_client_documents(
    client_id: int,
    include_archived: Optional[bool] = Query(None, description="Include archived documents"),
    document_types: Optional[List[str]] = Query(None, description="Filter by document types"),
    file_extensions: Optional[List[str]] = Query(None, description="Filter by file extensions"),
    limit: Optional[int] = Query(50, ge=1, le=500, description="Maximum documents to return"),
    offset: Optional[int] = Query(0, ge=0, description="Number of documents to skip"),
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get all documents accessible to a specific client based on their folder permissions
    """
    # Verify client belongs to current owner
    client = db.query(Client).filter(
        Client.id == client_id,
        Client.owner_id == current_owner.id
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Get accessible documents using folder access service
    documents = folder_access_service.get_accessible_documents(
        client=client,
        db=db,
        include_archived=include_archived,
        document_types=document_types,
        file_extensions=file_extensions,
        limit=limit,
        offset=offset
    )
    
    return documents


@router.get("/{client_id}/documents/{document_id}", response_model=DocumentResponse, summary="Get specific document if accessible")
async def get_client_document(
    client_id: int,
    document_id: int,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get a specific document if the client has access to it
    """
    # Verify client belongs to current owner
    client = db.query(Client).filter(
        Client.id == client_id,
        Client.owner_id == current_owner.id
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Get the document
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.owner_id == current_owner.id
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Check if client can access this document
    if not folder_access_service.can_access_document(client, document):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Client does not have access to this document"
        )
    
    return document


@router.get("/{client_id}/folder-structure", summary="Get client accessible folder structure")
async def get_client_folder_structure(
    client_id: int,
    max_depth: int = Query(3, ge=1, le=10, description="Maximum folder depth to traverse"),
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get the folder structure accessible to a client
    """
    # Verify client belongs to current owner
    client = db.query(Client).filter(
        Client.id == client_id,
        Client.owner_id == current_owner.id
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Get folder structure using folder access service
    folder_structure = folder_access_service.get_folder_structure(
        client=client,
        db=db,
        max_depth=max_depth
    )
    
    return folder_structure


@router.get("/{client_id}/access-summary", summary="Get client document access summary")
async def get_client_access_summary(
    client_id: int,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get a summary of document access statistics for a client
    """
    # Verify client belongs to current owner
    client = db.query(Client).filter(
        Client.id == client_id,
        Client.owner_id == current_owner.id
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Get access summary using folder access service
    access_summary = folder_access_service.get_document_access_summary(
        client=client,
        db=db
    )
    
    return access_summary


@router.post("/{client_id}/validate-folder-access", summary="Validate folder paths for client")
async def validate_client_folder_access(
    client_id: int,
    folder_paths: List[str],
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Validate that folder paths exist and contain documents
    """
    # Verify client belongs to current owner
    client = db.query(Client).filter(
        Client.id == client_id,
        Client.owner_id == current_owner.id
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Validate folder paths using folder access service
    validation_result = folder_access_service.validate_folder_paths(
        folder_paths=folder_paths,
        owner_id=current_owner.id,
        db=db
    )
    
    return validation_result


@router.patch("/{client_id}/folder-access", summary="Update client folder access")
async def update_client_folder_access(
    client_id: int,
    folder_paths: List[str],
    operation: str = Query(..., pattern="^(add|remove|replace)$", description="Operation to perform"),
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Update client folder access with add/remove/replace operations
    """
    # Verify client belongs to current owner
    client = db.query(Client).filter(
        Client.id == client_id,
        Client.owner_id == current_owner.id
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Update folder access using folder access service
    result = folder_access_service.update_client_folder_access(
        client=client,
        folder_paths=folder_paths,
        operation=operation,
        db=db
    )
    
    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result.get("error", "Failed to update folder access")
        )
    
    return result


@router.post("/bulk-folder-access", summary="Update folder access for multiple clients")
async def update_bulk_client_folder_access(
    folder_access_data: ClientFolderAccessUpdate,
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Update folder access for multiple clients at once
    """
    results = {
        "successful_updates": [],
        "failed_updates": [],
        "total_attempted": len(folder_access_data.client_ids)
    }
    
    for client_id in folder_access_data.client_ids:
        try:
            # Verify client belongs to current owner
            client = db.query(Client).filter(
                Client.id == client_id,
                Client.owner_id == current_owner.id
            ).first()
            
            if not client:
                results["failed_updates"].append({
                    "client_id": client_id,
                    "error": "Client not found"
                })
                continue
            
            # Update folder access
            result = folder_access_service.update_client_folder_access(
                client=client,
                folder_paths=folder_access_data.folder_paths,
                operation=folder_access_data.operation,
                db=db
            )
            
            if result["success"]:
                results["successful_updates"].append({
                    "client_id": client_id,
                    "client_name": client.full_name,
                    "result": result
                })
            else:
                results["failed_updates"].append({
                    "client_id": client_id,
                    "error": result.get("error", "Unknown error")
                })
                
        except Exception as e:
            results["failed_updates"].append({
                "client_id": client_id,
                "error": str(e)
            })
    
    results["total_successful"] = len(results["successful_updates"])
    results["total_failed"] = len(results["failed_updates"])
    
    return results


@router.get("/{client_id}/searchable-content", summary="Get client searchable document content")
async def get_client_searchable_content(
    client_id: int,
    limit: Optional[int] = Query(100, ge=1, le=1000, description="Maximum embeddings to return"),
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get searchable document embeddings accessible to a client (for AI search preparation)
    """
    # Verify client belongs to current owner
    client = db.query(Client).filter(
        Client.id == client_id,
        Client.owner_id == current_owner.id
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Get accessible embeddings using folder access service
    embeddings = folder_access_service.get_accessible_embeddings(
        client=client,
        db=db,
        limit=limit
    )
    
    # Return summary information (not the actual embeddings for security)
    embedding_summary = []
    for embedding in embeddings:
        embedding_summary.append({
            "id": embedding.id,
            "document_id": embedding.document_id,
            "chunk_index": embedding.chunk_index,
            "text_preview": embedding.text_chunk[:200] + "..." if len(embedding.text_chunk) > 200 else embedding.text_chunk,
            "embedding_model": embedding.embedding_model,
            "vector_dimension": embedding.vector_dimension,
            "processed_at": embedding.processed_at,
            "document_name": embedding.document.filename if embedding.document else "Unknown"
        })
    
    return {
        "total_embeddings": len(embedding_summary),
        "client_id": client_id,
        "client_name": client.full_name,
        "embeddings": embedding_summary
    }


@router.get("/folder-tree/{owner_id}", summary="Get complete folder tree for owner")
async def get_owner_folder_tree(
    max_depth: int = Query(5, ge=1, le=10, description="Maximum folder depth"),
    db: Session = Depends(get_db),
    current_owner: Owner = Depends(get_current_active_owner)
):
    """
    Get complete folder tree for the owner (for folder selection UI)
    """
    # Get all documents for the owner
    documents = db.query(Document).filter(
        Document.owner_id == current_owner.id,
        Document.sync_status.in_(["completed", "syncing"])
    ).all()
    
    # Build folder tree structure
    folder_tree = {
        "name": "Root",
        "type": "folder", 
        "path": "/",
        "children": {},
        "document_count": 0
    }
    
    for doc in documents:
        _add_document_to_folder_tree(folder_tree, doc, max_depth)
    
    # Convert to list format
    return _folder_tree_to_list(folder_tree)


def _add_document_to_folder_tree(folder_node, document, max_depth, current_depth=0):
    """Helper function to add document to folder tree"""
    if current_depth >= max_depth:
        return
    
    path_parts = document.dropbox_path.strip("/").split("/")
    current_node = folder_node
    
    # Navigate/create folder structure
    for i, part in enumerate(path_parts[:-1]):  # Exclude filename
        if current_depth + i >= max_depth:
            break
            
        if part not in current_node["children"]:
            current_node["children"][part] = {
                "name": part,
                "type": "folder",
                "path": "/" + "/".join(path_parts[:i+1]),
                "children": {},
                "document_count": 0
            }
        
        current_node = current_node["children"][part]
    
    # Increment document count
    current_node["document_count"] += 1


def _folder_tree_to_list(node):
    """Helper function to convert folder tree to list format"""
    children = []
    for child in node["children"].values():
        children.append(_folder_tree_to_list(child))
    
    return {
        "name": node["name"],
        "type": node["type"],
        "path": node["path"],
        "children": children,
        "document_count": node.get("document_count", 0),
        "selectable": True  # All folders are selectable for assignment
    }
