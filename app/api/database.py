"""
Database management API endpoints
"""
from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException
from sqlalchemy.orm import Session
from ..core.database import get_db, create_tables, drop_tables
from ..models import Owner
from ..schemas import OwnerCreate, OwnerResponse
from passlib.context import CryptContext

router = APIRouter(prefix="/database", tags=["Database Management"])

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


@router.post("/init", summary="Initialize database tables")
async def initialize_database():
    """Initialize all database tables"""
    try:
        create_tables()
        return {"message": "Database tables created successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create tables: {str(e)}")


@router.delete("/reset", summary="Reset database (Development only)")
async def reset_database():
    """Drop and recreate all database tables - USE WITH CAUTION"""
    try:
        drop_tables()
        create_tables()
        return {"message": "Database reset successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reset database: {str(e)}")


@router.get("/test-connection", summary="Test database connection")
async def test_database_connection(db: Session = Depends(get_db)):
    """Test database connection and basic query"""
    try:
        # Try a simple query
        result = db.execute("SELECT 1 as test").fetchone()
        owner_count = db.query(Owner).count()
        
        return {
            "status": "connected",
            "test_query": result[0] if result else None,
            "owner_count": owner_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database connection failed: {str(e)}")


@router.post("/create-test-owner", response_model=OwnerResponse, summary="Create test owner")
async def create_test_owner(db: Session = Depends(get_db)):
    """Create a test owner for development purposes"""
    
    # Check if test owner already exists
    existing_owner = db.query(Owner).filter(Owner.email == "<EMAIL>").first()
    if existing_owner:
        raise HTTPException(status_code=400, detail="Test owner already exists")
    
    # Create test owner
    test_owner_data = OwnerCreate(
        email="<EMAIL>",
        full_name="Test CA Owner",
        firm_name="Test CA Firm",
        phone="******-0123",
        address="123 Test Street, Test City, TC 12345",
        password="testpassword123"
    )
    
    # Hash password
    hashed_password = pwd_context.hash(test_owner_data.password)
    
    # Create owner object
    db_owner = Owner(
        email=test_owner_data.email,
        full_name=test_owner_data.full_name,
        firm_name=test_owner_data.firm_name,
        phone=test_owner_data.phone,
        address=test_owner_data.address,
        hashed_password=hashed_password,
        is_active=True,
        is_verified=True
    )
    
    try:
        db.add(db_owner)
        db.commit()
        db.refresh(db_owner)
        return db_owner
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create test owner: {str(e)}")


@router.get("/owners", summary="List all owners")
async def list_owners(db: Session = Depends(get_db)):
    """List all owners in the database"""
    try:
        owners = db.query(Owner).all()
        return {
            "count": len(owners),
            "owners": [
                {
                    "id": owner.id,
                    "email": owner.email,
                    "full_name": owner.full_name,
                    "firm_name": owner.firm_name,
                    "is_active": owner.is_active,
                    "created_at": owner.created_at
                }
                for owner in owners
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list owners: {str(e)}")
