"""
Authentication dependencies for FastAPI endpoints
"""
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional

from .database import get_db
from .security import verify_token
from ..models import Owner

# Security scheme
security = HTTPBearer()


async def get_current_owner(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> Owner:
    """
    Get the current authenticated owner from JWT token
    """
    # Verify and decode the token
    payload = verify_token(credentials.credentials)
    
    # Extract owner email from token
    owner_email = payload.get("sub")
    if not owner_email:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Get owner from database
    owner = db.query(Owner).filter(Owner.email == owner_email).first()
    if not owner:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Owner not found",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Check if owner is active
    if not owner.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive owner"
        )
    
    return owner


async def get_current_active_owner(
    current_owner: Owner = Depends(get_current_owner)
) -> Owner:
    """
    Get the current active owner (additional check for active status)
    """
    if not current_owner.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive owner"
        )
    return current_owner


# Optional authentication dependency (for endpoints that work with or without auth)
async def get_current_owner_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: Session = Depends(get_db)
) -> Optional[Owner]:
    """
    Get the current owner if authenticated, otherwise return None
    """
    if not credentials:
        return None
    
    try:
        payload = verify_token(credentials.credentials)
        owner_email = payload.get("sub")
        if not owner_email:
            return None
        
        owner = db.query(Owner).filter(Owner.email == owner_email).first()
        if not owner or not owner.is_active:
            return None
        
        return owner
    except HTTPException:
        return None
