"""
Application configuration settings
"""
from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    """Application settings"""
    
    # Basic app settings
    app_name: str = "CA Firm Document Management"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # Database settings
    database_url: Optional[str] = None
    postgres_user: str = os.getenv("POSTGRES_USER", "indianic")  # Default to current user
    postgres_password: str = os.getenv("POSTGRES_PASSWORD", "")  # No password for local dev
    postgres_host: str = os.getenv("POSTGRES_HOST", "localhost")
    postgres_port: str = os.getenv("POSTGRES_PORT", "5432")
    postgres_db: str = os.getenv("POSTGRES_DB", "cafirm_db")
    
    # Security settings
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Redis settings (for Celery)
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_url: str = "redis://localhost:6379/0"
    
    # Dropbox settings (for development - replace with real values)
    dropbox_app_key: Optional[str] = "tkdur8x2u3as5ka"
    dropbox_app_secret: Optional[str] = "988ayesb1gyudfl"
    
    # API settings
    api_v1_str: str = "/api/v1"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    @property
    def database_url_sync(self) -> str:
        """Get synchronous database URL with psycopg3 driver"""
        if self.database_url:
            return self.database_url
        # Use psycopg (psycopg3) driver explicitly
        password_part = f":{self.postgres_password}" if self.postgres_password else ""
        return f"postgresql+psycopg://{self.postgres_user}{password_part}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
    
    @property
    def database_url_async(self) -> str:
        """Get asynchronous database URL with asyncpg driver"""
        if self.database_url:
            return self.database_url.replace("postgresql://", "postgresql+asyncpg://")
        password_part = f":{self.postgres_password}" if self.postgres_password else ""
        return f"postgresql+asyncpg://{self.postgres_user}{password_part}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"

# Global settings instance
settings = Settings()
