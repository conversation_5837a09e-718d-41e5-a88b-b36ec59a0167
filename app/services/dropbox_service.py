"""
Dropbox integration service for OAuth and file operations
"""
import dropbox
from dropbox.oauth import DropboxOAuth2<PERSON>low, DropboxOAuth2FlowNoRedirect
import secrets
import logging
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session

from ..core.config import settings
from ..models import DropboxIntegration, Owner
from ..schemas.dropbox import DropboxAccountInfo, DropboxFolderItem, DropboxFolderContents, DropboxConnectionTest

logger = logging.getLogger(__name__)


class DropboxService:
    """Service for managing Dropbox OAuth and file operations"""
    
    def __init__(self):
        # These will be set when Dropbox app is created
        self.app_key = settings.dropbox_app_key or "tkdur8x2u3as5ka"
        self.app_secret = settings.dropbox_app_secret or "988ayesb1gyudfl"
        self.redirect_uri = "http://localhost:8000/api/owner/dropbox/callback"
        


    def get_authorization_url(self) -> str:
        """
        Generate Dropbox OAuth authorization URL for manual code entry
        Returns: auth_url
        """
        # Use DropboxOAuth2FlowNoRedirect for manual code entry
        oauth_flow = DropboxOAuth2FlowNoRedirect(
            consumer_key=self.app_key,
            consumer_secret=self.app_secret,
            token_access_type='offline'  # For refresh tokens
        )
        
        # Get authorization URL
        auth_url = oauth_flow.start()
        
        return auth_url
    
    def exchange_code_for_token(self, auth_code: str) -> Dict[str, Any]:
        """
        Exchange authorization code for access token (manual entry)
        """
        try:
            # Create OAuth flow for finishing the authorization
            oauth_flow = DropboxOAuth2FlowNoRedirect(
                consumer_key=self.app_key,
                consumer_secret=self.app_secret,
                token_access_type='offline'
            )
            
            # Exchange code for token
            result = oauth_flow.finish(auth_code)
            
            return {
                'access_token': result.access_token,
                'refresh_token': result.refresh_token,
                'account_id': result.account_id,
                'user_id': result.user_id,
                'expires_at': getattr(result, 'expires_at', None)  # This might be None for long-lived tokens
            }
            
        except Exception as e:
            logger.error(f"Error exchanging code for token: {str(e)}")
            raise Exception(f"Failed to exchange authorization code: {str(e)}")
    
    def get_account_info(self, access_token: str) -> DropboxAccountInfo:
        """
        Get Dropbox account information using access token
        """
        try:
            dbx = dropbox.Dropbox(access_token)
            account = dbx.users_get_current_account()
            
            return DropboxAccountInfo(
                account_id=account.account_id,
                email=account.email,
                name=account.name.display_name,
                country=account.country,
                locale=account.locale
            )
            
        except Exception as e:
            logger.error(f"Error getting account info: {str(e)}")
            raise Exception(f"Failed to get account information: {str(e)}")
    
    def refresh_access_token(self, refresh_token: str) -> Dict[str, Any]:
        """
        Refresh the access token using the refresh token
        """
        import requests
        import base64
        
        try:
            logger.info("Attempting to refresh Dropbox access token")
            
            # Prepare authorization header (Base64 encoded app_key:app_secret)
            auth_string = f"{self.app_key}:{self.app_secret}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            
            # Make POST request to Dropbox token endpoint
            url = "https://api.dropbox.com/oauth2/token"
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Authorization': f'Basic {auth_b64}'
            }
            data = {
                'grant_type': 'refresh_token',
                'refresh_token': refresh_token
            }
            
            response = requests.post(url, headers=headers, data=data)
            
            if response.status_code == 200:
                token_data = response.json()
                logger.info("Successfully refreshed Dropbox access token")
                
                return {
                    'access_token': token_data['access_token'],
                    'refresh_token': token_data.get('refresh_token', refresh_token),  # Use existing if not provided
                    'expires_at': token_data.get('expires_in', None)
                }
            else:
                error_msg = f"Token refresh failed with status {response.status_code}: {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            
        except Exception as e:
            logger.error(f"Failed to refresh access token: {str(e)}")
            raise Exception(f"Token refresh failed: {str(e)}")

    def test_connection(self, access_token: str) -> DropboxConnectionTest:
        """
        Test Dropbox connection and return status
        """
        try:
            dbx = dropbox.Dropbox(access_token)
            
            # Test basic API access
            account = dbx.users_get_current_account()
            
            # Test folder access
            try:
                dbx.files_list_folder("")
                folder_access = True
            except:
                folder_access = False
            
            account_info = DropboxAccountInfo(
                account_id=account.account_id,
                email=account.email,
                name=account.name.display_name
            )
            
            permissions = ['files.metadata.read', 'files.content.read']
            if folder_access:
                permissions.append('folder_access')
                
            return DropboxConnectionTest(
                connected=True,
                account_info=account_info,
                permissions=permissions,
                error=None
            )
            
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            return DropboxConnectionTest(
                connected=False,
                account_info=None,
                permissions=[],
                error=str(e)
            )
    
    def list_folder_contents(self, access_token: str, folder_path: str = "") -> DropboxFolderContents:
        """
        List contents of a Dropbox folder
        """
        try:
            dbx = dropbox.Dropbox(access_token)
            
            # List folder contents
            result = dbx.files_list_folder(folder_path)
            
            items = []
            for entry in result.entries:
                if isinstance(entry, dropbox.files.FolderMetadata):
                    items.append(DropboxFolderItem(
                        name=entry.name,
                        path=entry.path_lower,
                        is_folder=True
                    ))
                elif isinstance(entry, dropbox.files.FileMetadata):
                    file_ext = entry.name.lower().split('.')[-1] if '.' in entry.name else ''
                    items.append(DropboxFolderItem(
                        name=entry.name,
                        path=entry.path_lower,
                        is_folder=False,
                        size=entry.size,
                        modified=entry.server_modified.isoformat() if entry.server_modified else None,
                        file_type=file_ext
                    ))
            
            return DropboxFolderContents(
                path=folder_path,
                items=items,
                has_more=result.has_more,
                cursor=result.cursor if result.has_more else None
            )
            
        except Exception as e:
            logger.error(f"Error listing folder contents: {str(e)}")
            raise Exception(f"Failed to list folder contents: {str(e)}")
    
    def get_folder_stats(self, access_token: str, folder_path: str = "") -> Dict[str, Any]:
        """
        Get statistics for a folder (file count, total size)
        """
        try:
            dbx = dropbox.Dropbox(access_token)
            
            # List folder contents
            result = dbx.files_list_folder(folder_path, recursive=False)
            
            file_count = 0
            folder_count = 0
            total_size = 0
            
            for entry in result.entries:
                if isinstance(entry, dropbox.files.FolderMetadata):
                    folder_count += 1
                elif isinstance(entry, dropbox.files.FileMetadata):
                    file_count += 1
                    total_size += entry.size
            
            # Continue if there are more entries
            while result.has_more:
                result = dbx.files_list_folder_continue(result.cursor)
                for entry in result.entries:
                    if isinstance(entry, dropbox.files.FolderMetadata):
                        folder_count += 1
                    elif isinstance(entry, dropbox.files.FileMetadata):
                        file_count += 1
                        total_size += entry.size
            
            return {
                'file_count': file_count,
                'folder_count': folder_count,
                'total_size': total_size,
                'total_items': file_count + folder_count
            }
            
        except Exception as e:
            logger.error(f"Error getting folder stats: {str(e)}")
            # Return default values on error
            return {
                'file_count': 0,
                'folder_count': 0,
                'total_size': 0,
                'total_items': 0
            }
    
    def download_file(self, access_token: str, file_path: str) -> bytes:
        """
        Download file content from Dropbox
        
        Args:
            access_token: Dropbox access token
            file_path: Path to the file in Dropbox
        
        Returns:
            File content as bytes
        """
        try:
            dbx = dropbox.Dropbox(access_token)
            
            # Download file
            metadata, response = dbx.files_download(file_path)
            
            logger.info(f"Downloaded file {file_path} ({metadata.size} bytes)")
            return response.content
            
        except Exception as e:
            logger.error(f"Error downloading file {file_path}: {str(e)}")
            raise Exception(f"Failed to download file: {str(e)}")
    
    def get_file_metadata(self, access_token: str, file_path: str) -> Dict[str, Any]:
        """
        Get detailed metadata for a file
        
        Args:
            access_token: Dropbox access token
            file_path: Path to the file in Dropbox
        
        Returns:
            File metadata dictionary
        """
        try:
            dbx = dropbox.Dropbox(access_token)
            
            # Get file metadata
            metadata = dbx.files_get_metadata(file_path)
            
            if isinstance(metadata, dropbox.files.FileMetadata):
                file_metadata = {
                    'dropbox_file_id': metadata.id,
                    'name': metadata.name,
                    'path_lower': metadata.path_lower,
                    'path_display': metadata.path_display,
                    'size': metadata.size,
                    'server_modified': metadata.server_modified.isoformat() if metadata.server_modified else None,
                    'client_modified': metadata.client_modified.isoformat() if metadata.client_modified else None,
                    'rev': metadata.rev,
                    'content_hash': metadata.content_hash,
                    'is_downloadable': metadata.is_downloadable,
                    'export_info': None
                }
                
                # Check if file has export info (for Google Docs, etc.)
                if hasattr(metadata, 'export_info') and metadata.export_info:
                    file_metadata['export_info'] = {
                        'export_as': metadata.export_info.export_as,
                        'export_options': metadata.export_info.export_options
                    }
                
                return file_metadata
            else:
                raise Exception("Path does not point to a file")
                
        except Exception as e:
            logger.error(f"Error getting file metadata for {file_path}: {str(e)}")
            raise Exception(f"Failed to get file metadata: {str(e)}")
    
    def list_files_recursive(self, access_token: str, folder_path: str = "", file_types: List[str] = None) -> List[Dict[str, Any]]:
        """
        Recursively list all files in a folder and its subfolders
        
        Args:
            access_token: Dropbox access token
            folder_path: Starting folder path
            file_types: Optional list of file extensions to filter by
        
        Returns:
            List of file metadata dictionaries
        """
        try:
            dbx = dropbox.Dropbox(access_token)
            
            all_files = []
            
            # Start recursive listing
            result = dbx.files_list_folder(folder_path, recursive=True)
            
            while True:
                for entry in result.entries:
                    if isinstance(entry, dropbox.files.FileMetadata):
                        # Check file type filter
                        if file_types:
                            file_ext = entry.name.lower().split('.')[-1] if '.' in entry.name else ''
                            if file_ext not in [ft.lower().lstrip('.') for ft in file_types]:
                                continue
                        
                        file_info = {
                            'dropbox_file_id': entry.id,
                            'name': entry.name,
                            'path_lower': entry.path_lower,
                            'path_display': entry.path_display,
                            'size': entry.size,
                            'server_modified': entry.server_modified.isoformat() if entry.server_modified else None,
                            'client_modified': entry.client_modified.isoformat() if entry.client_modified else None,
                            'rev': entry.rev,
                            'content_hash': entry.content_hash,
                            'is_downloadable': entry.is_downloadable
                        }
                        all_files.append(file_info)
                
                if not result.has_more:
                    break
                    
                result = dbx.files_list_folder_continue(result.cursor)
            
            logger.info(f"Found {len(all_files)} files in {folder_path}")
            return all_files
            
        except Exception as e:
            logger.error(f"Error listing files recursively in {folder_path}: {str(e)}")
            raise Exception(f"Failed to list files: {str(e)}")
    
    def create_integration(
        self, 
        db: Session, 
        owner: Owner, 
        access_token: str, 
        refresh_token: Optional[str] = None,
        account_info: Optional[DropboxAccountInfo] = None
    ) -> DropboxIntegration:
        """
        Create or update Dropbox integration for an owner
        """
        try:
            # Check if integration already exists
            existing = db.query(DropboxIntegration).filter(
                DropboxIntegration.owner_id == owner.id
            ).first()
            
            if existing:
                # Update existing integration
                existing.access_token = access_token  # Should encrypt this
                existing.refresh_token = refresh_token
                if account_info:
                    existing.account_id = account_info.account_id
                    existing.email = account_info.email
                    existing.name = account_info.name
                existing.sync_status = "connected"
                existing.last_error = None
                
                db.commit()
                db.refresh(existing)
                return existing
            else:
                # Create new integration
                integration = DropboxIntegration(
                    owner_id=owner.id,
                    access_token=access_token,  # Should encrypt this
                    refresh_token=refresh_token,
                    account_id=account_info.account_id if account_info else None,
                    email=account_info.email if account_info else None,
                    name=account_info.name if account_info else None,
                    sync_status="connected"
                )
                
                db.add(integration)
                db.commit()
                db.refresh(integration)
                return integration
                
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating/updating integration: {str(e)}")
            raise Exception(f"Failed to save integration: {str(e)}")
    
    def disconnect_integration(self, db: Session, integration: DropboxIntegration):
        """
        Disconnect and remove Dropbox integration
        """
        try:
            db.delete(integration)
            db.commit()
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error disconnecting integration: {str(e)}")
            raise Exception(f"Failed to disconnect integration: {str(e)}")


# Global service instance
dropbox_service = DropboxService()
