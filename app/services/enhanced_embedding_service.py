"""
Enhanced embedding service for generating and managing document embeddings for AI search
Supports multiple embedding models and providers
"""
import json
import logging
import hashlib
import asyncio
from typing import List, Optional, Tuple, Dict, Any, Union
from sqlalchemy.orm import Session
from datetime import datetime
import re
import math

# Embedding model imports (commented out until API keys are available)
# from sentence_transformers import SentenceTransformer
# import numpy as np
# from sklearn.metrics.pairwise import cosine_similarity
# import openai
# from transformers import AutoTokenizer, AutoModel
# import torch

from ..models import Document, DocumentEmbedding, Client
from ..core.config import settings

logger = logging.getLogger(__name__)


class EmbeddingModelConfig:
    """Configuration for different embedding models"""
    
    MODELS = {
        "sentence_transformers": {
            "all-MiniLM-L6-v2": {
                "dimension": 384,
                "max_tokens": 256,
                "description": "Fast and efficient, good for general purpose",
                "requires_api_key": False
            },
            "all-mpnet-base-v2": {
                "dimension": 768,
                "max_tokens": 384,
                "description": "Higher quality embeddings, slower",
                "requires_api_key": False
            }
        },
        "openai": {
            "text-embedding-ada-002": {
                "dimension": 1536,
                "max_tokens": 8191,
                "description": "OpenAI's embedding model, requires API key",
                "requires_api_key": True
            },
            "text-embedding-3-small": {
                "dimension": 1536,
                "max_tokens": 8191,
                "description": "OpenAI's newer embedding model, more efficient",
                "requires_api_key": True
            }
        },
        "huggingface": {
            "BAAI/bge-small-en-v1.5": {
                "dimension": 384,
                "max_tokens": 512,
                "description": "BGE model, good performance",
                "requires_api_key": False
            }
        }
    }
    
    @classmethod
    def get_model_info(cls, provider: str, model_name: str) -> Dict[str, Any]:
        """Get information about a specific model"""
        return cls.MODELS.get(provider, {}).get(model_name, {})
    
    @classmethod
    def list_available_models(cls) -> Dict[str, List[str]]:
        """List all available models by provider"""
        return {provider: list(models.keys()) for provider, models in cls.MODELS.items()}


class EnhancedEmbeddingService:
    """
    Enhanced service for generating and managing document embeddings for semantic search
    Supports multiple embedding providers and models
    """
    
    def __init__(self, provider: str = "sentence_transformers", model_name: str = "all-MiniLM-L6-v2"):
        self.provider = provider
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        
        # Get model configuration
        self.model_config = EmbeddingModelConfig.get_model_info(provider, model_name)
        self.vector_dimension = self.model_config.get("dimension", 384)
        self.max_tokens = self.model_config.get("max_tokens", 256)
        
        # Initialize model (commented out until dependencies are available)
        self._initialize_model()
        
        logger.info(f"EmbeddingService initialized with {provider}/{model_name} (dimension: {self.vector_dimension})")
    
    def _initialize_model(self):
        """Initialize the embedding model based on provider"""
        try:
            if self.provider == "sentence_transformers" and self.model_config.get("requires_api_key", True):
                # When sentence_transformers is available:
                # from sentence_transformers import SentenceTransformer
                # self.model = SentenceTransformer(self.model_name)
                logger.warning(f"Sentence Transformers model {self.model_name} not loaded - install sentence-transformers")
                
            elif self.provider == "openai":
                # Requires OpenAI API key
                # openai.api_key = settings.openai_api_key
                logger.warning("OpenAI embeddings not available - set OPENAI_API_KEY")
                
            elif self.provider == "huggingface":
                # When transformers is available:
                # from transformers import AutoTokenizer, AutoModel
                # self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                # self.model = AutoModel.from_pretrained(self.model_name)
                logger.warning(f"HuggingFace model {self.model_name} not loaded - install transformers")
                
        except Exception as e:
            logger.error(f"Failed to initialize embedding model {self.provider}/{self.model_name}: {e}")
            self.model = None
    
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding vector for text
        
        Args:
            text: Input text to generate embedding for
            
        Returns:
            List of floats representing the embedding vector
        """
        if not text or not text.strip():
            return [0.0] * self.vector_dimension
        
        # Clean and truncate text
        cleaned_text = self._preprocess_text(text)
        
        try:
            if self.provider == "sentence_transformers" and self.model:
                # When model is available:
                # embedding = self.model.encode(cleaned_text)
                # return embedding.tolist()
                pass
                
            elif self.provider == "openai" and hasattr(self, 'openai_client'):
                # When OpenAI is available:
                # response = openai.Embedding.create(
                #     input=cleaned_text,
                #     model=self.model_name
                # )
                # return response['data'][0]['embedding']
                pass
                
            elif self.provider == "huggingface" and self.model:
                # When transformers is available:
                # inputs = self.tokenizer(cleaned_text, return_tensors="pt", truncation=True, max_length=self.max_tokens)
                # with torch.no_grad():
                #     outputs = self.model(**inputs)
                #     embedding = outputs.last_hidden_state.mean(dim=1).squeeze().numpy()
                # return embedding.tolist()
                pass
            
            # Fallback: Generate deterministic dummy embedding based on text
            return self._generate_dummy_embedding(cleaned_text)
            
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            return self._generate_dummy_embedding(cleaned_text)
    
    def process_document_for_embeddings(
        self, 
        document_id: int, 
        db: Session,
        force_regenerate: bool = False,
        chunk_size: int = None,
        chunk_overlap: int = None
    ) -> Dict[str, Any]:
        """
        Process a document and generate embeddings for text chunks
        
        Args:
            document_id: ID of the document to process
            db: Database session
            force_regenerate: Force regeneration even if embeddings exist
            chunk_size: Custom chunk size (uses default if None)
            chunk_overlap: Custom chunk overlap (uses default if None)
            
        Returns:
            Dictionary with processing results
        """
        start_time = datetime.utcnow()
        result = {
            "document_id": document_id,
            "success": False,
            "chunks_processed": 0,
            "chunks_failed": 0,
            "processing_time_seconds": 0,
            "error": None
        }
        
        try:
            # Get the document
            document = db.query(Document).filter(Document.id == document_id).first()
            if not document:
                result["error"] = f"Document {document_id} not found"
                return result
            
            if not document.extracted_text:
                result["error"] = f"Document {document_id} has no extracted text"
                return result
            
            # Check if embeddings already exist and we're not forcing regeneration
            existing_embeddings = db.query(DocumentEmbedding).filter(
                DocumentEmbedding.document_id == document_id
            ).count()
            
            if existing_embeddings > 0 and not force_regenerate:
                result["error"] = f"Embeddings already exist for document {document_id} (use force_regenerate=True)"
                return result
            
            # Clear existing embeddings if regenerating
            if force_regenerate:
                db.query(DocumentEmbedding).filter(
                    DocumentEmbedding.document_id == document_id
                ).delete()
            
            # Split text into chunks
            chunk_size = chunk_size or self._calculate_optimal_chunk_size(document.extracted_text)
            chunk_overlap = chunk_overlap or min(100, chunk_size // 10)
            
            chunks = self._split_text_into_chunks(
                document.extracted_text, 
                chunk_size=chunk_size,
                overlap=chunk_overlap
            )
            
            logger.info(f"Processing {len(chunks)} chunks for document {document_id}")
            
            # Generate embeddings for each chunk
            for i, chunk_info in enumerate(chunks):
                try:
                    chunk_text = chunk_info["text"]
                    
                    if len(chunk_text.strip()) < 20:  # Skip very short chunks
                        continue
                    
                    # Generate embedding
                    embedding_vector = self.generate_embedding(chunk_text)
                    
                    # Calculate text hash for deduplication
                    text_hash = hashlib.md5(chunk_text.encode()).hexdigest()
                    
                    # Create DocumentEmbedding record
                    embedding_record = DocumentEmbedding(
                        document_id=document_id,
                        chunk_index=i,
                        text_chunk=chunk_text,
                        chunk_start_char=chunk_info.get("start_char"),
                        chunk_end_char=chunk_info.get("end_char"),
                        embedding_model=f"{self.provider}/{self.model_name}",
                        vector_dimension=self.vector_dimension,
                        processing_status="completed",
                        processed_at=datetime.utcnow(),
                        is_searchable=True,
                        relevance_score=self._calculate_chunk_relevance(chunk_text)
                    )
                    
                    # Set the embedding vector
                    embedding_record.set_embedding_vector(embedding_vector)
                    
                    db.add(embedding_record)
                    result["chunks_processed"] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to generate embedding for chunk {i} of document {document_id}: {e}")
                    result["chunks_failed"] += 1
                    continue
            
            # Update document status
            document.embeddings_generated = True
            document.searchable = True
            
            try:
                db.commit()
                result["success"] = True
                logger.info(f"Generated embeddings for document {document_id}: {result['chunks_processed']} chunks")
                
            except Exception as e:
                db.rollback()
                result["error"] = f"Database commit failed: {e}"
                
        except Exception as e:
            db.rollback()
            result["error"] = str(e)
            logger.error(f"Failed to process document {document_id} for embeddings: {e}")
        
        finally:
            end_time = datetime.utcnow()
            result["processing_time_seconds"] = (end_time - start_time).total_seconds()
        
        return result
    
    def search_similar_chunks(
        self,
        query_text: str,
        client: Client,
        db: Session,
        limit: int = 10,
        similarity_threshold: float = 0.6,
        document_ids: Optional[List[int]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar document chunks using semantic similarity
        
        Args:
            query_text: Query text to search for
            client: Client for access filtering
            db: Database session
            limit: Maximum number of results
            similarity_threshold: Minimum similarity score
            document_ids: Optional list of specific document IDs to search
            
        Returns:
            List of similar chunks with metadata
        """
        try:
            # Generate embedding for query
            query_embedding = self.generate_embedding(query_text)
            
            # Get accessible document IDs for the client
            from .folder_access_service import FolderAccessService
            folder_service = FolderAccessService()
            accessible_docs = folder_service.get_accessible_documents(client, db)
            accessible_doc_ids = [doc.id for doc in accessible_docs]
            
            if document_ids:
                # Intersection of requested and accessible documents
                accessible_doc_ids = list(set(document_ids) & set(accessible_doc_ids))
            
            if not accessible_doc_ids:
                return []
            
            # Get embeddings for accessible documents
            embeddings_query = db.query(DocumentEmbedding).filter(
                DocumentEmbedding.document_id.in_(accessible_doc_ids),
                DocumentEmbedding.processing_status == "completed",
                DocumentEmbedding.is_searchable == True
            ).join(Document)
            
            embeddings = embeddings_query.all()
            
            if not embeddings:
                return []
            
            # Calculate similarity scores
            results = []
            for embedding in embeddings:
                try:
                    doc_embedding = embedding.get_embedding_vector()
                    
                    # Calculate cosine similarity
                    similarity = self._calculate_cosine_similarity(query_embedding, doc_embedding)
                    
                    if similarity >= similarity_threshold:
                        results.append({
                            "document_id": embedding.document_id,
                            "document_name": embedding.document.filename,
                            "document_path": embedding.document.dropbox_path,
                            "chunk_index": embedding.chunk_index,
                            "text_chunk": embedding.text_chunk,
                            "similarity_score": similarity,
                            "embedding_id": embedding.id,
                            "chunk_start_char": embedding.chunk_start_char,
                            "chunk_end_char": embedding.chunk_end_char,
                            "relevance_score": embedding.relevance_score
                        })
                        
                except Exception as e:
                    logger.error(f"Error calculating similarity for embedding {embedding.id}: {e}")
                    continue
            
            # Sort by similarity score and return top results
            results.sort(key=lambda x: x["similarity_score"], reverse=True)
            return results[:limit]
            
        except Exception as e:
            logger.error(f"Error in similarity search: {e}")
            return []
    
    def get_embedding_statistics(self, db: Session, owner_id: Optional[int] = None) -> Dict[str, Any]:
        """Get statistics about embeddings in the system"""
        try:
            query = db.query(DocumentEmbedding)
            
            if owner_id:
                query = query.join(Document).filter(Document.owner_id == owner_id)
            
            embeddings = query.all()
            
            stats = {
                "total_embeddings": len(embeddings),
                "by_model": {},
                "by_status": {},
                "by_dimension": {},
                "total_documents_with_embeddings": len(set(e.document_id for e in embeddings)),
                "average_chunks_per_document": 0,
                "processing_errors": 0
            }
            
            # Calculate distributions
            for embedding in embeddings:
                # By model
                model = embedding.embedding_model
                stats["by_model"][model] = stats["by_model"].get(model, 0) + 1
                
                # By status
                status = embedding.processing_status
                stats["by_status"][status] = stats["by_status"].get(status, 0) + 1
                
                # By dimension
                dim = str(embedding.vector_dimension)
                stats["by_dimension"][dim] = stats["by_dimension"].get(dim, 0) + 1
                
                # Count errors
                if embedding.processing_status == "failed":
                    stats["processing_errors"] += 1
            
            # Calculate average chunks per document
            if stats["total_documents_with_embeddings"] > 0:
                stats["average_chunks_per_document"] = round(
                    stats["total_embeddings"] / stats["total_documents_with_embeddings"], 2
                )
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting embedding statistics: {e}")
            return {"error": str(e)}
    
    def cleanup_embeddings(self, db: Session, owner_id: Optional[int] = None) -> Dict[str, int]:
        """Clean up orphaned or failed embeddings"""
        try:
            cleanup_stats = {
                "orphaned_removed": 0,
                "failed_removed": 0,
                "duplicates_removed": 0
            }
            
            # Remove embeddings for non-existent documents
            orphaned_query = db.query(DocumentEmbedding).outerjoin(Document).filter(
                Document.id.is_(None)
            )
            
            if owner_id:
                orphaned_query = orphaned_query.filter(Document.owner_id == owner_id)
            
            orphaned_count = orphaned_query.count()
            orphaned_query.delete(synchronize_session=False)
            cleanup_stats["orphaned_removed"] = orphaned_count
            
            # Remove failed embeddings older than 7 days
            failed_cutoff = datetime.utcnow() - timedelta(days=7)
            failed_query = db.query(DocumentEmbedding).filter(
                DocumentEmbedding.processing_status == "failed",
                DocumentEmbedding.processed_at < failed_cutoff
            )
            
            if owner_id:
                failed_query = failed_query.join(Document).filter(Document.owner_id == owner_id)
            
            failed_count = failed_query.count()
            failed_query.delete(synchronize_session=False)
            cleanup_stats["failed_removed"] = failed_count
            
            db.commit()
            return cleanup_stats
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error cleaning up embeddings: {e}")
            return {"error": str(e)}
    
    # Private helper methods
    
    def _preprocess_text(self, text: str) -> str:
        """Clean and preprocess text for embedding"""
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special characters that might cause issues
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)]', ' ', text)
        
        # Truncate to max tokens if necessary
        words = text.split()
        if len(words) > self.max_tokens:
            text = ' '.join(words[:self.max_tokens])
        
        return text
    
    def _split_text_into_chunks(
        self, 
        text: str, 
        chunk_size: int = 1000, 
        overlap: int = 100
    ) -> List[Dict[str, Any]]:
        """Split text into overlapping chunks with metadata"""
        if not text:
            return []
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Try to break at sentence boundary
            if end < len(text):
                # Look for sentence endings within reasonable distance
                for i in range(min(200, chunk_size // 4)):
                    if end - i > start and text[end - i] in '.!?':
                        end = end - i + 1
                        break
            
            chunk_text = text[start:end].strip()
            if chunk_text:
                chunks.append({
                    "text": chunk_text,
                    "start_char": start,
                    "end_char": end,
                    "length": len(chunk_text)
                })
            
            # Move start position with overlap
            start = end - overlap
            if start >= len(text):
                break
        
        return chunks
    
    def _calculate_optimal_chunk_size(self, text: str) -> int:
        """Calculate optimal chunk size based on text length and model capacity"""
        text_length = len(text)
        
        # Base chunk size on model capacity and text length
        if text_length < 500:
            return min(text_length, 200)
        elif text_length < 2000:
            return 500
        elif text_length < 10000:
            return 800
        else:
            return 1000
    
    def _calculate_chunk_relevance(self, chunk_text: str) -> float:
        """Calculate relevance score for a text chunk"""
        # Simple heuristic based on text characteristics
        score = 0.5  # Base score
        
        # Boost score for longer chunks (more content)
        if len(chunk_text) > 100:
            score += 0.1
        
        # Boost score for chunks with numbers (often important in finance documents)
        if re.search(r'\d+', chunk_text):
            score += 0.1
        
        # Boost score for chunks with financial keywords
        financial_keywords = ['tax', 'income', 'revenue', 'expense', 'profit', 'loss', 'balance', 'asset', 'liability']
        keyword_count = sum(1 for keyword in financial_keywords if keyword.lower() in chunk_text.lower())
        score += min(0.3, keyword_count * 0.1)
        
        return min(1.0, score)
    
    def _generate_dummy_embedding(self, text: str) -> List[float]:
        """Generate a deterministic dummy embedding based on text content"""
        # Create a deterministic embedding based on text hash
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        # Convert hash to numbers
        embedding = []
        for i in range(0, len(text_hash), 2):
            hex_pair = text_hash[i:i+2]
            value = int(hex_pair, 16) / 255.0  # Normalize to 0-1
            embedding.append(value)
        
        # Pad or truncate to desired dimension
        while len(embedding) < self.vector_dimension:
            embedding.extend(embedding)
        
        return embedding[:self.vector_dimension]
    
    def _calculate_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            # Ensure vectors are the same length
            if len(vec1) != len(vec2):
                return 0.0
            
            # Calculate dot product
            dot_product = sum(a * b for a, b in zip(vec1, vec2))
            
            # Calculate magnitudes
            magnitude1 = math.sqrt(sum(a * a for a in vec1))
            magnitude2 = math.sqrt(sum(a * a for a in vec2))
            
            # Avoid division by zero
            if magnitude1 == 0 or magnitude2 == 0:
                return 0.0
            
            # Calculate cosine similarity
            similarity = dot_product / (magnitude1 * magnitude2)
            
            # Ensure similarity is between 0 and 1
            return max(0.0, min(1.0, similarity))
            
        except Exception as e:
            logger.error(f"Error calculating cosine similarity: {e}")
            return 0.0
