"""
Embedding service for generating and managing document embeddings for AI search
NOTE: This requires API keys for embedding models - implement when keys are available
"""
import json
import logging
from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime

# Import for future use when API keys are available
# from sentence_transformers import SentenceTransformer
# import numpy as np
# from sklearn.metrics.pairwise import cosine_similarity

from ..models import Document, DocumentEmbedding, Client
from ..core.database import get_db

logger = logging.getLogger(__name__)


class EmbeddingService:
    """
    Service for generating and managing document embeddings for semantic search
    
    TODO: Implement when embedding API keys are available
    """
    
    def __init__(self):
        # Initialize embedding model when API keys are available
        # self.model = SentenceTransformer('all-MiniLM-L6-v2')
        # self.vector_dimension = 384  # Dimension for all-MiniLM-L6-v2
        self.model = None
        self.vector_dimension = 384
        logger.info("EmbeddingService initialized (model loading disabled - needs API keys)")
    
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding vector for text
        
        Args:
            text: Input text to generate embedding for
            
        Returns:
            List of floats representing the embedding vector
            
        TODO: Implement when embedding model is available
        """
        if not self.model:
            # Return dummy embedding for now
            logger.warning("Embedding model not available - returning dummy embedding")
            return [0.0] * self.vector_dimension
        
        # When model is available:
        # embedding = self.model.encode(text)
        # return embedding.tolist()
        
        return [0.0] * self.vector_dimension
    
    def process_document_for_embeddings(self, document_id: int, db: Session) -> bool:
        """
        Process a document and generate embeddings for text chunks
        
        Args:
            document_id: ID of the document to process
            db: Database session
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get the document
            document = db.query(Document).filter(Document.id == document_id).first()
            if not document:
                logger.error(f"Document {document_id} not found")
                return False
            
            if not document.extracted_text:
                logger.warning(f"Document {document_id} has no extracted text")
                return False
            
            # Split text into chunks
            chunks = self._split_text_into_chunks(document.extracted_text)
            
            # Clear existing embeddings for this document
            db.query(DocumentEmbedding).filter(
                DocumentEmbedding.document_id == document_id
            ).delete()
            
            # Generate embeddings for each chunk
            for i, chunk_text in enumerate(chunks):
                if len(chunk_text.strip()) < 10:  # Skip very short chunks
                    continue
                
                try:
                    # Generate embedding
                    embedding_vector = self.generate_embedding(chunk_text)
                    
                    # Create DocumentEmbedding record
                    embedding_record = DocumentEmbedding(
                        document_id=document_id,
                        chunk_index=i,
                        text_chunk=chunk_text,
                        chunk_start_char=None,  # TODO: Calculate actual positions
                        chunk_end_char=None,
                        embedding_model="all-MiniLM-L6-v2",  # Update when real model is used
                        vector_dimension=self.vector_dimension,
                        processing_status="completed",
                        processed_at=datetime.utcnow(),
                        is_searchable=True
                    )
                    
                    # Set the embedding vector
                    embedding_record.set_embedding_vector(embedding_vector)
                    
                    db.add(embedding_record)
                    
                except Exception as e:
                    logger.error(f"Failed to generate embedding for chunk {i} of document {document_id}: {e}")
                    continue
            
            # Update document status
            document.embeddings_generated = True
            document.searchable = True
            
            db.commit()
            logger.info(f"Generated embeddings for document {document_id} with {len(chunks)} chunks")
            return True
            
        except Exception as e:
            logger.error(f"Failed to process document {document_id} for embeddings: {e}")
            db.rollback()
            return False
    
    def search_similar_documents(
        self, 
        question: str, 
        client_id: int, 
        db: Session,
        limit: int = 5,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search for similar document chunks using cosine similarity
        
        Args:
            question: User's question
            client_id: ID of the client making the search
            db: Database session
            limit: Maximum number of results to return
            similarity_threshold: Minimum similarity score
            
        Returns:
            List of dictionaries with document chunks and similarity scores
        """
        try:
            # Get client and their folder permissions
            client = db.query(Client).filter(Client.id == client_id).first()
            if not client:
                logger.error(f"Client {client_id} not found")
                return []
            
            allowed_paths = client.get_assigned_folder_paths()
            if not allowed_paths:
                logger.warning(f"Client {client_id} has no assigned folder paths")
                return []
            
            # Generate embedding for the question
            question_embedding = self.generate_embedding(question)
            
            # Get all embeddings for documents in allowed folders
            query = db.query(DocumentEmbedding, Document).join(Document).filter(
                DocumentEmbedding.is_searchable == True,
                Document.owner_id == client.owner_id,
                DocumentEmbedding.processing_status == "completed"
            )
            
            # Filter by folder permissions
            folder_filters = []
            for path in allowed_paths:
                folder_filters.append(Document.dropbox_path.like(f"{path}%"))
            
            if folder_filters:
                query = query.filter(db.or_(*folder_filters))
            
            embeddings_with_docs = query.all()
            
            if not embeddings_with_docs:
                logger.info(f"No searchable documents found for client {client_id}")
                return []
            
            # Calculate similarity scores
            results = []
            for embedding, document in embeddings_with_docs:
                try:
                    # Get the embedding vector
                    doc_embedding = embedding.get_embedding_vector()
                    
                    # Calculate cosine similarity (placeholder implementation)
                    # When numpy is available:
                    # similarity = cosine_similarity([question_embedding], [doc_embedding])[0][0]
                    similarity = 0.5  # Placeholder similarity score
                    
                    if similarity >= similarity_threshold:
                        results.append({
                            'document_id': document.id,
                            'document_name': document.filename,
                            'document_path': document.dropbox_path,
                            'chunk_index': embedding.chunk_index,
                            'text_chunk': embedding.text_chunk,
                            'similarity_score': similarity,
                            'embedding_id': embedding.id
                        })
                
                except Exception as e:
                    logger.error(f"Error calculating similarity for embedding {embedding.id}: {e}")
                    continue
            
            # Sort by similarity score and return top results
            results.sort(key=lambda x: x['similarity_score'], reverse=True)
            return results[:limit]
            
        except Exception as e:
            logger.error(f"Error in similarity search for client {client_id}: {e}")
            return []
    
    def _split_text_into_chunks(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """
        Split text into overlapping chunks for better context preservation
        
        Args:
            text: Input text to split
            chunk_size: Maximum size of each chunk
            overlap: Number of characters to overlap between chunks
            
        Returns:
            List of text chunks
        """
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Try to break at sentence boundary
            if end < len(text):
                # Look for sentence endings within reasonable distance
                for i in range(min(100, chunk_size // 4)):
                    if end - i > start and text[end - i] in '.!?':
                        end = end - i + 1
                        break
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Move start position with overlap
            start = end - overlap
            if start >= len(text):
                break
        
        return chunks
    
    def get_document_embeddings_count(self, document_id: int, db: Session) -> int:
        """Get the number of embeddings for a document"""
        return db.query(DocumentEmbedding).filter(
            DocumentEmbedding.document_id == document_id,
            DocumentEmbedding.processing_status == "completed"
        ).count()
    
    def delete_document_embeddings(self, document_id: int, db: Session) -> bool:
        """Delete all embeddings for a document"""
        try:
            deleted_count = db.query(DocumentEmbedding).filter(
                DocumentEmbedding.document_id == document_id
            ).delete()
            
            db.commit()
            logger.info(f"Deleted {deleted_count} embeddings for document {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete embeddings for document {document_id}: {e}")
            db.rollback()
            return False
    
    def regenerate_embeddings_for_client_documents(self, client_id: int, db: Session) -> Dict[str, int]:
        """
        Regenerate embeddings for all documents accessible to a client
        
        Returns:
            Dictionary with statistics about the regeneration process
        """
        try:
            client = db.query(Client).filter(Client.id == client_id).first()
            if not client:
                return {"error": "Client not found"}
            
            allowed_paths = client.get_assigned_folder_paths()
            if not allowed_paths:
                return {"error": "No assigned folder paths"}
            
            # Get documents in allowed folders
            query = db.query(Document).filter(
                Document.owner_id == client.owner_id,
                Document.text_extracted == True
            )
            
            folder_filters = []
            for path in allowed_paths:
                folder_filters.append(Document.dropbox_path.like(f"{path}%"))
            
            if folder_filters:
                query = query.filter(db.or_(*folder_filters))
            
            documents = query.all()
            
            stats = {
                "total_documents": len(documents),
                "processed": 0,
                "failed": 0,
                "skipped": 0
            }
            
            for document in documents:
                if self.process_document_for_embeddings(document.id, db):
                    stats["processed"] += 1
                else:
                    stats["failed"] += 1
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to regenerate embeddings for client {client_id}: {e}")
            return {"error": str(e)}
