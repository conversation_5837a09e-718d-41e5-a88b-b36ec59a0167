"""
File storage service for managing downloaded files
"""
import os
import hashlib
import shutil
from pathlib import Path
from typing import Optional, Dict, Any, BinaryIO
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class FileStorageService:
    """Service for storing and managing downloaded files"""
    
    def __init__(self, base_storage_path: str = "storage"):
        """
        Initialize file storage service
        
        Args:
            base_storage_path: Base directory for file storage
        """
        self.base_path = Path(base_storage_path)
        self.ensure_storage_directories()
    
    def ensure_storage_directories(self):
        """Create necessary storage directories"""
        directories = [
            self.base_path,
            self.base_path / "documents",
            self.base_path / "temp",
            self.base_path / "processed",
            self.base_path / "thumbnails"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Ensured directory exists: {directory}")
    
    def get_file_path(self, owner_id: int, document_id: int, filename: str, subfolder: str = "documents") -> Path:
        """
        Get the storage path for a file
        
        Args:
            owner_id: Owner ID
            document_id: Document ID
            filename: Original filename
            subfolder: Storage subfolder (documents, temp, processed, thumbnails)
        
        Returns:
            Path object for the file
        """
        # Create owner-specific directory structure
        owner_dir = self.base_path / subfolder / f"owner_{owner_id}"
        owner_dir.mkdir(parents=True, exist_ok=True)
        
        # Create filename with document ID to avoid conflicts
        safe_filename = self.sanitize_filename(filename)
        final_filename = f"{document_id}_{safe_filename}"
        
        return owner_dir / final_filename
    
    def sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename to be filesystem-safe
        
        Args:
            filename: Original filename
        
        Returns:
            Sanitized filename
        """
        # Remove or replace problematic characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limit length
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:190] + ext
        
        return filename
    
    def save_file(self, file_data: bytes, file_path: Path) -> Dict[str, Any]:
        """
        Save file data to storage
        
        Args:
            file_data: Binary file data
            file_path: Path where to save the file
        
        Returns:
            Dictionary with file metadata
        """
        try:
            # Ensure parent directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file
            with open(file_path, 'wb') as f:
                f.write(file_data)
            
            # Calculate file metadata
            file_size = len(file_data)
            content_hash = hashlib.sha256(file_data).hexdigest()
            
            # Get file stats
            stat = file_path.stat()
            
            metadata = {
                'file_path': str(file_path),
                'file_size_bytes': file_size,
                'content_hash': content_hash,
                'stored_at': datetime.utcnow().isoformat(),
                'storage_success': True
            }
            
            logger.info(f"Successfully saved file: {file_path} ({file_size} bytes)")
            return metadata
            
        except Exception as e:
            logger.error(f"Failed to save file {file_path}: {str(e)}")
            return {
                'storage_success': False,
                'error': str(e)
            }
    
    def file_exists(self, file_path: Path) -> bool:
        """Check if file exists in storage"""
        return file_path.exists() and file_path.is_file()
    
    def get_file_info(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        Get information about stored file
        
        Args:
            file_path: Path to the file
        
        Returns:
            File information dictionary or None if file doesn't exist
        """
        if not self.file_exists(file_path):
            return None
        
        try:
            stat = file_path.stat()
            
            # Calculate content hash
            with open(file_path, 'rb') as f:
                content_hash = hashlib.sha256(f.read()).hexdigest()
            
            return {
                'file_path': str(file_path),
                'file_size_bytes': stat.st_size,
                'content_hash': content_hash,
                'modified_at': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'created_at': datetime.fromtimestamp(stat.st_ctime).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {str(e)}")
            return None
    
    def delete_file(self, file_path: Path) -> bool:
        """
        Delete file from storage
        
        Args:
            file_path: Path to the file to delete
        
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            if self.file_exists(file_path):
                file_path.unlink()
                logger.info(f"Deleted file: {file_path}")
                return True
            else:
                logger.warning(f"File not found for deletion: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {str(e)}")
            return False
    
    def move_file(self, source_path: Path, destination_path: Path) -> bool:
        """
        Move file from one location to another
        
        Args:
            source_path: Current file path
            destination_path: New file path
        
        Returns:
            True if moved successfully, False otherwise
        """
        try:
            # Ensure destination directory exists
            destination_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(str(source_path), str(destination_path))
            logger.info(f"Moved file from {source_path} to {destination_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to move file from {source_path} to {destination_path}: {str(e)}")
            return False
    
    def get_storage_stats(self, owner_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Get storage statistics
        
        Args:
            owner_id: Optional owner ID to get stats for specific owner
        
        Returns:
            Storage statistics
        """
        try:
            if owner_id:
                # Get stats for specific owner
                owner_path = self.base_path / "documents" / f"owner_{owner_id}"
                if not owner_path.exists():
                    return {'total_files': 0, 'total_size_bytes': 0}
                
                paths_to_check = [owner_path]
            else:
                # Get stats for all storage
                paths_to_check = [self.base_path]
            
            total_files = 0
            total_size = 0
            
            for base_path in paths_to_check:
                for file_path in base_path.rglob('*'):
                    if file_path.is_file():
                        total_files += 1
                        total_size += file_path.stat().st_size
            
            return {
                'total_files': total_files,
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'total_size_gb': round(total_size / (1024 * 1024 * 1024), 2)
            }
            
        except Exception as e:
            logger.error(f"Failed to get storage stats: {str(e)}")
            return {
                'total_files': 0,
                'total_size_bytes': 0,
                'error': str(e)
            }
    
    def cleanup_temp_files(self, older_than_hours: int = 24) -> int:
        """
        Clean up temporary files older than specified hours
        
        Args:
            older_than_hours: Remove temp files older than this many hours
        
        Returns:
            Number of files deleted
        """
        try:
            temp_path = self.base_path / "temp"
            if not temp_path.exists():
                return 0
            
            cutoff_time = datetime.now().timestamp() - (older_than_hours * 3600)
            deleted_count = 0
            
            for file_path in temp_path.rglob('*'):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    try:
                        file_path.unlink()
                        deleted_count += 1
                    except Exception as e:
                        logger.warning(f"Failed to delete temp file {file_path}: {str(e)}")
            
            logger.info(f"Cleaned up {deleted_count} temporary files")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup temp files: {str(e)}")
            return 0
