"""
Document filtering service for advanced document queries and access control
"""
import logging
from typing import List, Dict, Any, Optional, Set
from sqlalchemy.orm import Session, Query
from sqlalchemy import and_, or_, func, text
from datetime import datetime, timedelta

from ..models import Document, Client, Owner, DocumentEmbedding

logger = logging.getLogger(__name__)


class DocumentFilterService:
    """
    Service for advanced document filtering and querying with access control
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def build_document_query(
        self,
        db: Session,
        base_filters: Optional[Dict[str, Any]] = None,
        client_access_filter: Optional[Client] = None,
        owner_filter: Optional[Owner] = None
    ) -> Query:
        """
        Build a base document query with common filters
        
        Args:
            db: Database session
            base_filters: Basic filters to apply
            client_access_filter: Client for folder access filtering
            owner_filter: Owner for ownership filtering
            
        Returns:
            SQLAlchemy Query object
        """
        query = db.query(Document)
        
        # Apply owner filter
        if owner_filter:
            query = query.filter(Document.owner_id == owner_filter.id)
        
        # Apply client access filter
        if client_access_filter:
            query = self._apply_client_access_filter(query, client_access_filter)
        
        # Apply base filters
        if base_filters:
            query = self._apply_base_filters(query, base_filters)
        
        return query
    
    def search_documents(
        self,
        db: Session,
        search_params: Dict[str, Any],
        client: Optional[Client] = None,
        owner: Optional[Owner] = None
    ) -> Dict[str, Any]:
        """
        Advanced document search with multiple criteria
        
        Args:
            db: Database session
            search_params: Search parameters dictionary
            client: Client for access filtering (optional)
            owner: Owner for ownership filtering (optional)
            
        Returns:
            Dictionary with search results and metadata
        """
        try:
            # Build base query
            query = self.build_document_query(
                db=db,
                client_access_filter=client,
                owner_filter=owner
            )
            
            # Apply search filters
            query = self._apply_search_filters(query, search_params)
            
            # Get total count before pagination
            total_count = query.count()
            
            # Apply sorting
            query = self._apply_sorting(query, search_params.get('sort_by', 'modified'), 
                                     search_params.get('sort_order', 'desc'))
            
            # Apply pagination
            page = search_params.get('page', 1)
            page_size = search_params.get('page_size', 20)
            offset = (page - 1) * page_size
            
            documents = query.offset(offset).limit(page_size).all()
            
            # Calculate pagination metadata
            total_pages = (total_count + page_size - 1) // page_size
            
            return {
                "documents": documents,
                "total_count": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_previous": page > 1,
                "search_params": search_params
            }
            
        except Exception as e:
            self.logger.error(f"Error in document search: {e}")
            return {
                "documents": [],
                "total_count": 0,
                "page": 1,
                "page_size": 20,
                "total_pages": 0,
                "has_next": False,
                "has_previous": False,
                "error": str(e)
            }
    
    def get_document_statistics(
        self,
        db: Session,
        client: Optional[Client] = None,
        owner: Optional[Owner] = None,
        date_range_days: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get comprehensive document statistics
        
        Args:
            db: Database session
            client: Client for access filtering (optional)
            owner: Owner for ownership filtering (optional)
            date_range_days: Limit to documents from last N days
            
        Returns:
            Dictionary with various statistics
        """
        try:
            # Build base query
            query = self.build_document_query(
                db=db,
                client_access_filter=client,
                owner_filter=owner
            )
            
            # Apply date range filter if specified
            if date_range_days:
                cutoff_date = datetime.utcnow() - timedelta(days=date_range_days)
                query = query.filter(Document.dropbox_modified_at >= cutoff_date)
            
            documents = query.all()
            
            # Calculate statistics
            stats = {
                "total_documents": len(documents),
                "processed_documents": sum(1 for doc in documents if doc.is_processed),
                "searchable_documents": sum(1 for doc in documents if doc.searchable),
                "documents_with_embeddings": sum(1 for doc in documents if doc.embeddings_generated),
                "file_type_distribution": {},
                "extension_distribution": {},
                "size_distribution": {
                    "small": 0,    # < 1MB
                    "medium": 0,   # 1MB - 10MB
                    "large": 0,    # 10MB - 100MB
                    "xlarge": 0    # > 100MB
                },
                "sync_status_distribution": {},
                "folder_distribution": {},
                "processing_status": {
                    "text_extracted": sum(1 for doc in documents if doc.text_extracted),
                    "embeddings_generated": sum(1 for doc in documents if doc.embeddings_generated),
                    "fully_processed": sum(1 for doc in documents if doc.is_processed and doc.searchable)
                }
            }
            
            # Calculate distributions
            for doc in documents:
                # File type distribution
                file_type = doc.file_type_category
                stats["file_type_distribution"][file_type] = stats["file_type_distribution"].get(file_type, 0) + 1
                
                # Extension distribution
                ext = doc.file_extension or "unknown"
                stats["extension_distribution"][ext] = stats["extension_distribution"].get(ext, 0) + 1
                
                # Size distribution
                if doc.file_size_bytes:
                    size_mb = doc.file_size_bytes / (1024 * 1024)
                    if size_mb < 1:
                        stats["size_distribution"]["small"] += 1
                    elif size_mb < 10:
                        stats["size_distribution"]["medium"] += 1
                    elif size_mb < 100:
                        stats["size_distribution"]["large"] += 1
                    else:
                        stats["size_distribution"]["xlarge"] += 1
                
                # Sync status distribution
                sync_status = doc.sync_status
                stats["sync_status_distribution"][sync_status] = stats["sync_status_distribution"].get(sync_status, 0) + 1
                
                # Folder distribution
                folder = self._get_folder_from_path(doc.dropbox_path)
                stats["folder_distribution"][folder] = stats["folder_distribution"].get(folder, 0) + 1
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error calculating document statistics: {e}")
            return {
                "total_documents": 0,
                "error": str(e)
            }
    
    def find_duplicate_documents(
        self,
        db: Session,
        owner: Owner,
        by_content_hash: bool = True,
        by_filename: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Find duplicate documents based on content hash or filename
        
        Args:
            db: Database session
            owner: Owner to search within
            by_content_hash: Find duplicates by content hash
            by_filename: Find duplicates by filename
            
        Returns:
            List of duplicate document groups
        """
        try:
            duplicates = []
            
            if by_content_hash:
                # Find documents with same content hash
                hash_query = db.query(
                    Document.content_hash,
                    func.count(Document.id).label('count')
                ).filter(
                    and_(
                        Document.owner_id == owner.id,
                        Document.content_hash.isnot(None),
                        Document.content_hash != ''
                    )
                ).group_by(Document.content_hash).having(func.count(Document.id) > 1)
                
                for hash_result in hash_query.all():
                    duplicate_docs = db.query(Document).filter(
                        and_(
                            Document.owner_id == owner.id,
                            Document.content_hash == hash_result.content_hash
                        )
                    ).all()
                    
                    duplicates.append({
                        "type": "content_hash",
                        "identifier": hash_result.content_hash,
                        "count": hash_result.count,
                        "documents": duplicate_docs
                    })
            
            if by_filename:
                # Find documents with same filename
                filename_query = db.query(
                    Document.filename,
                    func.count(Document.id).label('count')
                ).filter(
                    Document.owner_id == owner.id
                ).group_by(Document.filename).having(func.count(Document.id) > 1)
                
                for filename_result in filename_query.all():
                    duplicate_docs = db.query(Document).filter(
                        and_(
                            Document.owner_id == owner.id,
                            Document.filename == filename_result.filename
                        )
                    ).all()
                    
                    duplicates.append({
                        "type": "filename",
                        "identifier": filename_result.filename,
                        "count": filename_result.count,
                        "documents": duplicate_docs
                    })
            
            return duplicates
            
        except Exception as e:
            self.logger.error(f"Error finding duplicate documents: {e}")
            return []
    
    def get_recently_modified_documents(
        self,
        db: Session,
        client: Optional[Client] = None,
        owner: Optional[Owner] = None,
        days: int = 7,
        limit: int = 50
    ) -> List[Document]:
        """
        Get recently modified documents
        
        Args:
            db: Database session
            client: Client for access filtering (optional)
            owner: Owner for ownership filtering (optional)
            days: Number of days to look back
            limit: Maximum number of documents to return
            
        Returns:
            List of recently modified documents
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            query = self.build_document_query(
                db=db,
                client_access_filter=client,
                owner_filter=owner
            )
            
            query = query.filter(
                Document.dropbox_modified_at >= cutoff_date
            ).order_by(
                Document.dropbox_modified_at.desc()
            ).limit(limit)
            
            return query.all()
            
        except Exception as e:
            self.logger.error(f"Error getting recently modified documents: {e}")
            return []
    
    # Private helper methods
    
    def _apply_client_access_filter(self, query: Query, client: Client) -> Query:
        """Apply client folder access restrictions to query"""
        folder_paths = client.get_assigned_folder_paths()
        if not folder_paths:
            # No access to any folders - return empty result
            return query.filter(text("1=0"))
        
        # Create OR conditions for each allowed folder path
        folder_filters = []
        for path in folder_paths:
            normalized_path = self._normalize_path(path)
            folder_filters.append(Document.dropbox_path.like(f"{normalized_path}%"))
        
        return query.filter(or_(*folder_filters))
    
    def _apply_base_filters(self, query: Query, filters: Dict[str, Any]) -> Query:
        """Apply basic filters to document query"""
        
        # Sync status filter
        if 'sync_status' in filters:
            if isinstance(filters['sync_status'], list):
                query = query.filter(Document.sync_status.in_(filters['sync_status']))
            else:
                query = query.filter(Document.sync_status == filters['sync_status'])
        
        # Processing status filters
        if 'is_processed' in filters:
            query = query.filter(Document.is_processed == filters['is_processed'])
        
        if 'searchable' in filters:
            query = query.filter(Document.searchable == filters['searchable'])
        
        if 'text_extracted' in filters:
            query = query.filter(Document.text_extracted == filters['text_extracted'])
        
        # File type filters
        if 'file_extensions' in filters:
            extensions = filters['file_extensions']
            if extensions:
                ext_filters = [Document.file_extension == ext for ext in extensions]
                query = query.filter(or_(*ext_filters))
        
        # Date range filters
        if 'modified_after' in filters:
            query = query.filter(Document.dropbox_modified_at >= filters['modified_after'])
        
        if 'modified_before' in filters:
            query = query.filter(Document.dropbox_modified_at <= filters['modified_before'])
        
        # Size filters
        if 'min_size' in filters:
            query = query.filter(Document.file_size_bytes >= filters['min_size'])
        
        if 'max_size' in filters:
            query = query.filter(Document.file_size_bytes <= filters['max_size'])
        
        return query
    
    def _apply_search_filters(self, query: Query, search_params: Dict[str, Any]) -> Query:
        """Apply text search filters"""
        
        # Filename search
        if 'filename' in search_params and search_params['filename']:
            search_term = f"%{search_params['filename']}%"
            query = query.filter(Document.filename.ilike(search_term))
        
        # Path search
        if 'path' in search_params and search_params['path']:
            search_term = f"%{search_params['path']}%"
            query = query.filter(Document.dropbox_path.ilike(search_term))
        
        # Content search (in extracted text)
        if 'content' in search_params and search_params['content']:
            search_term = f"%{search_params['content']}%"
            query = query.filter(Document.extracted_text.ilike(search_term))
        
        # General text search (filename OR path OR content)
        if 'query' in search_params and search_params['query']:
            search_term = f"%{search_params['query']}%"
            query = query.filter(
                or_(
                    Document.filename.ilike(search_term),
                    Document.dropbox_path.ilike(search_term),
                    Document.extracted_text.ilike(search_term)
                )
            )
        
        return query
    
    def _apply_sorting(self, query: Query, sort_by: str, sort_order: str) -> Query:
        """Apply sorting to document query"""
        
        sort_column = None
        
        if sort_by == 'filename':
            sort_column = Document.filename
        elif sort_by == 'size':
            sort_column = Document.file_size_bytes
        elif sort_by == 'created':
            sort_column = Document.dropbox_created_at
        elif sort_by == 'modified':
            sort_column = Document.dropbox_modified_at
        elif sort_by == 'synced':
            sort_column = Document.last_synced_at
        else:
            sort_column = Document.dropbox_modified_at  # Default
        
        if sort_order.lower() == 'desc':
            query = query.order_by(sort_column.desc())
        else:
            query = query.order_by(sort_column.asc())
        
        return query
    
    def _normalize_path(self, path: str) -> str:
        """Normalize folder path format"""
        if not path:
            return "/"
        
        if not path.startswith("/"):
            path = "/" + path
        
        if path != "/" and path.endswith("/"):
            path = path.rstrip("/")
        
        return path
    
    def _get_folder_from_path(self, file_path: str) -> str:
        """Extract folder name from file path"""
        if not file_path or file_path == "/":
            return "/"
        
        parts = file_path.strip("/").split("/")
        if len(parts) <= 1:
            return "/"
        
        return "/" + "/".join(parts[:-1])
