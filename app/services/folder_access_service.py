"""
Folder access control service for managing client document permissions
"""
import logging
import os
from typing import List, Dict, Any, Optional, Set, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from ..models import Client, Document, Owner, DocumentEmbedding
from ..schemas import DocumentResponse, DocumentSummary

logger = logging.getLogger(__name__)


class FolderAccessService:
    """
    Service for managing and enforcing folder-based access control for clients
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def validate_folder_paths(
        self, 
        folder_paths: List[str], 
        owner_id: int, 
        db: Session
    ) -> Dict[str, Any]:
        """
        Validate that folder paths exist and contain documents for the owner
        
        Args:
            folder_paths: List of folder paths to validate
            owner_id: ID of the owner
            db: Database session
            
        Returns:
            Dictionary with validation results
        """
        validation_result = {
            "valid_paths": [],
            "invalid_paths": [],
            "warnings": [],
            "document_counts": {}
        }
        
        if not folder_paths:
            validation_result["warnings"].append("No folder paths provided")
            return validation_result
        
        for path in folder_paths:
            # Normalize path (ensure it starts with / and doesn't end with /)
            normalized_path = self._normalize_path(path)
            
            # Check if path contains documents
            doc_count = db.query(Document).filter(
                and_(
                    Document.owner_id == owner_id,
                    Document.dropbox_path.like(f"{normalized_path}%"),
                    Document.sync_status.in_(["completed", "syncing"])
                )
            ).count()
            
            if doc_count > 0:
                validation_result["valid_paths"].append(normalized_path)
                validation_result["document_counts"][normalized_path] = doc_count
            else:
                validation_result["invalid_paths"].append(normalized_path)
                validation_result["warnings"].append(
                    f"No documents found in path: {normalized_path}"
                )
        
        return validation_result
    
    def get_accessible_documents(
        self, 
        client: Client, 
        db: Session,
        include_archived: Optional[bool] = None,
        document_types: Optional[List[str]] = None,
        file_extensions: Optional[List[str]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[Document]:
        """
        Get all documents accessible to a specific client based on their folder permissions
        
        Args:
            client: Client object
            db: Database session
            include_archived: Whether to include archived documents
            document_types: Filter by specific document types
            file_extensions: Filter by specific file extensions
            limit: Maximum number of documents to return
            offset: Number of documents to skip
            
        Returns:
            List of accessible Document objects
        """
        try:
            # Base query for owner's documents
            query = db.query(Document).filter(
                Document.owner_id == client.owner_id
            )
            
            # Apply folder access restrictions
            folder_paths = client.get_assigned_folder_paths()
            if folder_paths:
                # Create OR conditions for each allowed folder path
                folder_filters = []
                for path in folder_paths:
                    normalized_path = self._normalize_path(path)
                    folder_filters.append(Document.dropbox_path.like(f"{normalized_path}%"))
                
                if folder_filters:
                    query = query.filter(or_(*folder_filters))
            else:
                # No folder access assigned - return empty list
                return []
            
            # Apply document type filters
            if document_types:
                allowed_types = client.get_allowed_document_types()
                if allowed_types:
                    # Intersection of requested types and allowed types
                    filtered_types = list(set(document_types) & set(allowed_types))
                    if filtered_types:
                        type_filters = []
                        for doc_type in filtered_types:
                            type_filters.append(Document.processing_metadata['document_type'].astext == doc_type)
                        query = query.filter(or_(*type_filters))
                    else:
                        return []  # No matching document types
            
            # Apply file extension filters
            if file_extensions:
                allowed_extensions = client.get_allowed_file_extensions()
                if allowed_extensions:
                    # Intersection of requested extensions and allowed extensions
                    filtered_extensions = list(set(file_extensions) & set(allowed_extensions))
                    if filtered_extensions:
                        ext_filters = []
                        for ext in filtered_extensions:
                            ext_filters.append(Document.file_extension == ext)
                        query = query.filter(or_(*ext_filters))
                    else:
                        return []  # No matching file extensions
            
            # Handle archived documents
            if include_archived is None:
                include_archived = client.include_archived_documents
            
            if not include_archived:
                # Exclude archived documents (assuming archived means old sync status)
                query = query.filter(
                    Document.sync_status.in_(["completed", "syncing"])
                )
            
            # Only include searchable documents if they need text content
            query = query.filter(Document.is_processed == True)
            
            # Order by modification date (newest first)
            query = query.order_by(Document.dropbox_modified_at.desc())
            
            # Apply pagination
            if offset:
                query = query.offset(offset)
            if limit:
                query = query.limit(limit)
            
            documents = query.all()
            
            self.logger.info(
                f"Retrieved {len(documents)} accessible documents for client {client.id}"
            )
            
            return documents
            
        except Exception as e:
            self.logger.error(f"Error getting accessible documents for client {client.id}: {e}")
            return []
    
    def can_access_document(self, client: Client, document: Document) -> bool:
        """
        Check if a client can access a specific document
        
        Args:
            client: Client object
            document: Document object
            
        Returns:
            True if client can access the document, False otherwise
        """
        try:
            # Check if document belongs to the same owner
            if document.owner_id != client.owner_id:
                return False
            
            # Check folder access
            folder_paths = client.get_assigned_folder_paths()
            if not folder_paths:
                return False
            
            # Check if document path matches any allowed folder path
            document_path = document.dropbox_path
            for allowed_path in folder_paths:
                normalized_path = self._normalize_path(allowed_path)
                if document_path.startswith(normalized_path):
                    # Additional checks for document type and file extension
                    return self._check_document_type_access(client, document) and \
                           self._check_file_extension_access(client, document)
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking document access for client {client.id}: {e}")
            return False
    
    def get_folder_structure(
        self, 
        client: Client, 
        db: Session,
        max_depth: int = 3
    ) -> Dict[str, Any]:
        """
        Get the folder structure accessible to a client
        
        Args:
            client: Client object
            db: Database session
            max_depth: Maximum folder depth to traverse
            
        Returns:
            Dictionary representing the folder structure
        """
        try:
            accessible_docs = self.get_accessible_documents(client, db)
            
            folder_structure = {
                "root": {
                    "name": "Root",
                    "type": "folder",
                    "path": "/",
                    "children": {},
                    "document_count": 0
                }
            }
            
            for doc in accessible_docs:
                self._add_document_to_structure(
                    folder_structure["root"], 
                    doc, 
                    max_depth
                )
            
            # Convert nested dictionaries to lists for easier frontend consumption
            return self._structure_to_list(folder_structure["root"])
            
        except Exception as e:
            self.logger.error(f"Error getting folder structure for client {client.id}: {e}")
            return {"name": "Root", "type": "folder", "path": "/", "children": [], "document_count": 0}
    
    def get_document_access_summary(
        self, 
        client: Client, 
        db: Session
    ) -> Dict[str, Any]:
        """
        Get a summary of document access for a client
        
        Args:
            client: Client object
            db: Database session
            
        Returns:
            Dictionary with access summary statistics
        """
        try:
            accessible_docs = self.get_accessible_documents(client, db)
            
            # Count documents by type
            type_counts = {}
            extension_counts = {}
            folder_counts = {}
            
            for doc in accessible_docs:
                # Count by file type category
                file_type = doc.file_type_category
                type_counts[file_type] = type_counts.get(file_type, 0) + 1
                
                # Count by extension
                ext = doc.file_extension or "unknown"
                extension_counts[ext] = extension_counts.get(ext, 0) + 1
                
                # Count by folder
                folder = self._get_parent_folder(doc.dropbox_path)
                folder_counts[folder] = folder_counts.get(folder, 0) + 1
            
            return {
                "total_documents": len(accessible_docs),
                "document_types": type_counts,
                "file_extensions": extension_counts,
                "folders": folder_counts,
                "assigned_folder_paths": client.get_assigned_folder_paths(),
                "allowed_document_types": client.get_allowed_document_types(),
                "allowed_file_extensions": client.get_allowed_file_extensions(),
                "searchable_documents": sum(1 for doc in accessible_docs if doc.searchable),
                "processed_documents": sum(1 for doc in accessible_docs if doc.is_processed)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting access summary for client {client.id}: {e}")
            return {
                "total_documents": 0,
                "document_types": {},
                "file_extensions": {},
                "folders": {},
                "assigned_folder_paths": [],
                "allowed_document_types": [],
                "allowed_file_extensions": [],
                "searchable_documents": 0,
                "processed_documents": 0
            }
    
    def get_accessible_embeddings(
        self, 
        client: Client, 
        db: Session,
        query_text: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[DocumentEmbedding]:
        """
        Get document embeddings that are accessible to a client
        
        Args:
            client: Client object
            db: Database session
            query_text: Optional text to search for (for future similarity search)
            limit: Maximum number of embeddings to return
            
        Returns:
            List of accessible DocumentEmbedding objects
        """
        try:
            # Get accessible documents first
            accessible_docs = self.get_accessible_documents(client, db)
            document_ids = [doc.id for doc in accessible_docs]
            
            if not document_ids:
                return []
            
            # Query embeddings for accessible documents only
            query = db.query(DocumentEmbedding).filter(
                and_(
                    DocumentEmbedding.document_id.in_(document_ids),
                    DocumentEmbedding.processing_status == "completed",
                    DocumentEmbedding.is_searchable == True
                )
            )
            
            # Order by relevance or document modification date
            query = query.join(Document).order_by(Document.dropbox_modified_at.desc())
            
            if limit:
                query = query.limit(limit)
            
            embeddings = query.all()
            
            self.logger.info(
                f"Retrieved {len(embeddings)} accessible embeddings for client {client.id}"
            )
            
            return embeddings
            
        except Exception as e:
            self.logger.error(f"Error getting accessible embeddings for client {client.id}: {e}")
            return []
    
    def update_client_folder_access(
        self, 
        client: Client, 
        folder_paths: List[str], 
        operation: str,
        db: Session
    ) -> Dict[str, Any]:
        """
        Update client folder access with add/remove/replace operations
        
        Args:
            client: Client object
            folder_paths: List of folder paths
            operation: "add", "remove", or "replace"
            db: Database session
            
        Returns:
            Dictionary with operation results
        """
        try:
            current_paths = set(client.get_assigned_folder_paths())
            new_paths = set(self._normalize_path(path) for path in folder_paths)
            
            if operation == "add":
                updated_paths = current_paths.union(new_paths)
            elif operation == "remove":
                updated_paths = current_paths.difference(new_paths)
            elif operation == "replace":
                updated_paths = new_paths
            else:
                raise ValueError(f"Invalid operation: {operation}")
            
            # Validate the new paths
            validation_result = self.validate_folder_paths(
                list(updated_paths), 
                client.owner_id, 
                db
            )
            
            # Update client folder access
            client.assigned_folder_paths = list(updated_paths)
            db.commit()
            
            return {
                "operation": operation,
                "previous_paths": list(current_paths),
                "updated_paths": list(updated_paths),
                "validation": validation_result,
                "success": True
            }
            
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error updating folder access for client {client.id}: {e}")
            return {
                "operation": operation,
                "error": str(e),
                "success": False
            }
    
    # Private helper methods
    
    def _normalize_path(self, path: str) -> str:
        """Normalize folder path format"""
        if not path:
            return "/"
        
        # Ensure path starts with /
        if not path.startswith("/"):
            path = "/" + path
        
        # Remove trailing slash unless it's root
        if path != "/" and path.endswith("/"):
            path = path.rstrip("/")
        
        return path
    
    def _check_document_type_access(self, client: Client, document: Document) -> bool:
        """Check if client can access this document type"""
        allowed_types = client.get_allowed_document_types()
        if not allowed_types:
            return True  # No restrictions
        
        # Get document type from metadata or file category
        doc_type = None
        if document.processing_metadata:
            doc_type = document.processing_metadata.get("document_type")
        
        if not doc_type:
            doc_type = document.file_type_category
        
        return doc_type in allowed_types
    
    def _check_file_extension_access(self, client: Client, document: Document) -> bool:
        """Check if client can access this file extension"""
        allowed_extensions = client.get_allowed_file_extensions()
        if not allowed_extensions:
            return True  # No restrictions
        
        doc_extension = document.file_extension
        if not doc_extension:
            return True  # Allow files without extensions
        
        # Normalize extension (remove dot and lowercase)
        normalized_ext = doc_extension.lower().lstrip('.')
        normalized_allowed = [ext.lower().lstrip('.') for ext in allowed_extensions]
        
        return normalized_ext in normalized_allowed
    
    def _add_document_to_structure(
        self, 
        folder_node: Dict[str, Any], 
        document: Document, 
        max_depth: int,
        current_depth: int = 0
    ):
        """Add a document to the folder structure"""
        if current_depth >= max_depth:
            return
        
        path_parts = document.dropbox_path.strip("/").split("/")
        current_node = folder_node
        
        # Navigate/create folder structure
        for i, part in enumerate(path_parts[:-1]):  # Exclude filename
            if current_depth + i >= max_depth:
                break
                
            if part not in current_node["children"]:
                current_node["children"][part] = {
                    "name": part,
                    "type": "folder",
                    "path": "/" + "/".join(path_parts[:i+1]),
                    "children": {},
                    "document_count": 0
                }
            
            current_node = current_node["children"][part]
        
        # Add document to the final folder
        current_node["document_count"] += 1
        
        # Add document as a child if not at max depth
        if current_depth < max_depth:
            doc_name = document.filename
            current_node["children"][doc_name] = {
                "name": doc_name,
                "type": "document",
                "path": document.dropbox_path,
                "document_id": document.id,
                "file_extension": document.file_extension,
                "file_size": document.file_size_bytes,
                "modified_at": document.dropbox_modified_at.isoformat() if document.dropbox_modified_at else None,
                "is_searchable": document.searchable
            }
    
    def _structure_to_list(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Convert nested dictionary structure to list format"""
        children = []
        for child in node["children"].values():
            if child["type"] == "folder":
                children.append(self._structure_to_list(child))
            else:
                children.append(child)
        
        return {
            "name": node["name"],
            "type": node["type"], 
            "path": node["path"],
            "children": children,
            "document_count": node.get("document_count", 0)
        }
    
    def _get_parent_folder(self, file_path: str) -> str:
        """Get the parent folder of a file path"""
        if not file_path or file_path == "/":
            return "/"
        
        parts = file_path.strip("/").split("/")
        if len(parts) <= 1:
            return "/"
        
        return "/" + "/".join(parts[:-1])
    
    def get_owner_folder_tree(self, owner_id: int, db: Session) -> Dict[str, Any]:
        """
        Generate a hierarchical folder tree for all documents belonging to an owner.
        This is used for the client management interface to select which folders to assign.
        """
        documents = db.query(Document).filter(Document.owner_id == owner_id).all()
        
        if not documents:
            return {
                "name": "Root",
                "path": "/",
                "type": "folder",
                "children": [],
                "document_count": 0
            }
        
        # Build a set of all unique folder paths
        folder_paths: Set[str] = set()
        document_counts: Dict[str, int] = {}
        
        for doc in documents:
            # Get the directory path of the document
            dir_path = os.path.dirname(doc.dropbox_path)
            if not dir_path or dir_path == ".":
                dir_path = "/"
            else:
                # Normalize the path
                if not dir_path.startswith("/"):
                    dir_path = "/" + dir_path
                dir_path = dir_path.rstrip("/")
                if dir_path == "":
                    dir_path = "/"
            
            # Add all parent paths
            current_path = ""
            parts = dir_path.strip("/").split("/") if dir_path != "/" else []
            
            # Always include root
            folder_paths.add("/")
            document_counts["/"] = document_counts.get("/", 0) + 1
            
            # Add each parent folder
            for part in parts:
                if part:  # Skip empty parts
                    current_path = current_path + "/" + part
                    folder_paths.add(current_path)
                    document_counts[current_path] = document_counts.get(current_path, 0) + 1
        
        # Build the tree structure
        tree = {
            "name": "Root",
            "path": "/",
            "type": "folder",
            "children": [],
            "document_count": document_counts.get("/", 0)
        }
        
        # Create a map of path to node for easy lookup
        path_to_node = {"/": tree}
        
        # Sort paths to ensure parents are processed before children
        sorted_paths = sorted([p for p in folder_paths if p != "/"])
        
        for path in sorted_paths:
            # Get parent path
            parent_path = os.path.dirname(path)
            if parent_path == "" or parent_path == ".":
                parent_path = "/"
            
            # Create the node
            folder_name = os.path.basename(path)
            node = {
                "name": folder_name,
                "path": path,
                "type": "folder",
                "children": [],
                "document_count": document_counts.get(path, 0)
            }
            
            # Add to parent
            if parent_path in path_to_node:
                path_to_node[parent_path]["children"].append(node)
                path_to_node[path] = node
                
                # Sort children by name
                path_to_node[parent_path]["children"].sort(key=lambda x: x["name"])
        
        return tree
