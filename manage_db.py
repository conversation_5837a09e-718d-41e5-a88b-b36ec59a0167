#!/usr/bin/env python3
"""
Database management utility for CA Firm application
"""
import os
import sys
import subprocess
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.database import engine, create_tables, drop_tables
from app.core.config import settings

def run_alembic_command(*args):
    """Run an alembic command"""
    cmd = ["alembic"] + list(args)
    result = subprocess.run(cmd, capture_output=True, text=True)
    print(result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr)
    return result.returncode == 0

def create_migration(message):
    """Create a new migration"""
    print(f"Creating migration: {message}")
    return run_alembic_command("revision", "--autogenerate", "-m", message)

def upgrade_database():
    """Upgrade database to latest migration"""
    print("Upgrading database to latest migration...")
    return run_alembic_command("upgrade", "head")

def downgrade_database(revision="base"):
    """Downgrade database to specific revision"""
    print(f"Downgrading database to: {revision}")
    return run_alembic_command("downgrade", revision)

def check_migration_status():
    """Check current migration status"""
    print("Current migration status:")
    run_alembic_command("current")
    print("\nMigration history:")
    run_alembic_command("history")

def create_all_tables():
    """Create all tables using SQLAlchemy (for development)"""
    print("Creating all tables using SQLAlchemy...")
    try:
        create_tables()
        print("✅ All tables created successfully!")
    except Exception as e:
        print(f"❌ Error creating tables: {e}")

def drop_all_tables():
    """Drop all tables (DANGEROUS - use with caution)"""
    confirm = input("⚠️  This will DROP ALL TABLES! Type 'YES' to confirm: ")
    if confirm == "YES":
        try:
            drop_tables()
            print("✅ All tables dropped successfully!")
        except Exception as e:
            print(f"❌ Error dropping tables: {e}")
    else:
        print("Operation cancelled.")

def show_database_info():
    """Show database connection info"""
    print("Database Configuration:")
    print(f"  URL: {settings.database_url_sync}")
    print(f"  Host: {settings.postgres_host}")
    print(f"  Port: {settings.postgres_port}")
    print(f"  Database: {settings.postgres_db}")
    print(f"  User: {settings.postgres_user}")

def main():
    """Main CLI interface"""
    if len(sys.argv) < 2:
        print("Usage: python manage_db.py <command>")
        print("\nAvailable commands:")
        print("  info              - Show database connection info")
        print("  status            - Check migration status")
        print("  migrate <message> - Create new migration")
        print("  upgrade           - Apply all pending migrations")
        print("  downgrade <rev>   - Downgrade to specific revision")
        print("  create-tables     - Create all tables (development)")
        print("  drop-tables       - Drop all tables (DANGEROUS)")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "info":
        show_database_info()
    elif command == "status":
        check_migration_status()
    elif command == "migrate":
        if len(sys.argv) < 3:
            print("Usage: python manage_db.py migrate <message>")
            sys.exit(1)
        message = " ".join(sys.argv[2:])
        create_migration(message)
    elif command == "upgrade":
        upgrade_database()
    elif command == "downgrade":
        revision = sys.argv[2] if len(sys.argv) > 2 else "base"
        downgrade_database(revision)
    elif command == "create-tables":
        create_all_tables()
    elif command == "drop-tables":
        drop_all_tables()
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)

if __name__ == "__main__":
    main()
